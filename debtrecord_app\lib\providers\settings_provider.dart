import 'package:flutter/foundation.dart';
import '../database/database_helper.dart';

class SettingsProvider with ChangeNotifier {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  String _currency = 'IQD';
  String _companyName = 'سجل الديون';
  String _companyLogo = '';
  String _printerSize = 'A4';
  bool _hideAmounts = false;

  String get currency => _currency;
  String get companyName => _companyName;
  String get companyLogo => _companyLogo;
  String get printerSize => _printerSize;
  bool get hideAmounts => _hideAmounts;

  final Map<String, String> _currencies = {
    'IQD': 'د.ع',
    'USD': '\$',
    'EUR': '€',
    'GBP': '£',
    'SAR': 'ر.س',
    'AED': 'د.إ',
    'KWD': 'د.ك',
    'QAR': 'ر.ق',
    'BHD': 'د.ب',
    'OMR': 'ر.ع',
  };

  Map<String, String> get currencies => _currencies;

  String get currencySymbol => _currencies[_currency] ?? 'د.ع';

  Future<void> loadSettings() async {
    try {
      _currency = await _databaseHelper.getSetting('currency') ?? 'IQD';
      _companyName =
          await _databaseHelper.getSetting('company_name') ?? 'سجل الديون';
      _companyLogo = await _databaseHelper.getSetting('company_logo') ?? '';
      _printerSize = await _databaseHelper.getSetting('printer_size') ?? 'A4';
      _hideAmounts =
          (await _databaseHelper.getSetting('hide_amounts') ?? 'false') ==
          'true';
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading settings: $e');
    }
  }

  Future<void> setCurrency(String currency) async {
    try {
      await _databaseHelper.setSetting('currency', currency);
      _currency = currency;
      notifyListeners();
    } catch (e) {
      debugPrint('Error setting currency: $e');
    }
  }

  Future<void> setCompanyName(String name) async {
    try {
      await _databaseHelper.setSetting('company_name', name);
      _companyName = name;
      notifyListeners();
    } catch (e) {
      debugPrint('Error setting company name: $e');
    }
  }

  Future<void> setCompanyLogo(String logoPath) async {
    try {
      await _databaseHelper.setSetting('company_logo', logoPath);
      _companyLogo = logoPath;
      notifyListeners();
    } catch (e) {
      debugPrint('Error setting company logo: $e');
    }
  }

  Future<void> setPrinterSize(String size) async {
    try {
      await _databaseHelper.setSetting('printer_size', size);
      _printerSize = size;
      notifyListeners();
    } catch (e) {
      debugPrint('Error setting printer size: $e');
    }
  }

  Future<void> setHideAmounts(bool hide) async {
    try {
      await _databaseHelper.setSetting('hide_amounts', hide.toString());
      _hideAmounts = hide;
      notifyListeners();
    } catch (e) {
      debugPrint('Error setting hide amounts: $e');
    }
  }

  String formatAmount(double amount) {
    if (_hideAmounts) {
      return '***';
    }

    // Format number with Arabic-Indic numerals if needed
    String formattedAmount = amount.toStringAsFixed(0);

    // Add thousand separators
    if (formattedAmount.length > 3) {
      String result = '';
      int count = 0;
      for (int i = formattedAmount.length - 1; i >= 0; i--) {
        if (count == 3) {
          result = ',' + result;
          count = 0;
        }
        result = formattedAmount[i] + result;
        count++;
      }
      formattedAmount = result;
    }

    return '$formattedAmount $currencySymbol';
  }

  List<String> get printerSizes => ['صغير', 'متوسط', 'A4', 'A3'];

  String getPrinterSizeDisplayName(String size) {
    switch (size) {
      case 'صغير':
        return 'صغير (58mm)';
      case 'متوسط':
        return 'متوسط (80mm)';
      case 'A4':
        return 'A4 (210x297mm)';
      case 'A3':
        return 'A3 (297x420mm)';
      default:
        return size;
    }
  }
}
