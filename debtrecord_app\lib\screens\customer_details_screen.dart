import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import 'package:url_launcher/url_launcher.dart';
import '../models/customer.dart';
import '../models/transaction.dart';
import '../providers/transaction_provider.dart';
import '../providers/customer_provider.dart';
import '../providers/settings_provider.dart';
import '../utils/app_theme.dart';
import '../widgets/transaction_card.dart';
import 'add_transaction_screen.dart';

class CustomerDetailsScreen extends StatefulWidget {
  final Customer customer;

  const CustomerDetailsScreen({super.key, required this.customer});

  @override
  State<CustomerDetailsScreen> createState() => _CustomerDetailsScreenState();
}

class _CustomerDetailsScreenState extends State<CustomerDetailsScreen> {
  String _selectedSort = 'date';

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadTransactions();
    });
  }

  Future<void> _loadTransactions() async {
    final transactionProvider = Provider.of<TransactionProvider>(
      context,
      listen: false,
    );
    await transactionProvider.loadTransactions(widget.customer.id!);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: SafeArea(
        child: Column(
          children: [
            // Top Bar
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppTheme.surfaceColor,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Row(
                children: [
                  // Left side icons (Back, Print, Call)
                  Row(
                    children: [
                      IconButton(
                        onPressed: () => Navigator.pop(context),
                        icon: const Icon(Icons.arrow_back),
                        tooltip: 'رجوع',
                      ),
                      const SizedBox(width: 8),
                      IconButton(
                        onPressed: _onPrintPressed,
                        icon: Icon(MdiIcons.printer),
                        tooltip: 'طباعة',
                      ),
                      const SizedBox(width: 8),
                      IconButton(
                        onPressed: _onCallPressed,
                        icon: Icon(MdiIcons.phone),
                        tooltip: 'اتصال',
                      ),
                    ],
                  ),

                  const Spacer(),

                  // Right side - Customer name
                  Expanded(
                    child: Text(
                      widget.customer.name,
                      style: AppTheme.headingMedium,
                      textAlign: TextAlign.right,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // Amount Display
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Consumer<SettingsProvider>(
                builder: (context, settingsProvider, child) {
                  return Container(
                    padding: const EdgeInsets.all(20),
                    decoration: AppTheme.amountBoxDecoration,
                    child: Row(
                      children: [
                        // Left side - Amount icon
                        Icon(
                          MdiIcons.currencyUsd,
                          color: AppTheme.primaryColor,
                          size: 32,
                        ),

                        const Spacer(),

                        // Right side - Amount
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            Text(
                              settingsProvider.formatAmount(
                                widget.customer.remainingAmount,
                              ),
                              style: AppTheme.headingLarge.copyWith(
                                color:
                                    widget.customer.remainingAmount > 0
                                        ? AppTheme.errorColor
                                        : AppTheme.successColor,
                                fontWeight: FontWeight.bold,
                                fontSize: 28,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              'المبلغ المتبقي',
                              style: AppTheme.bodyMedium.copyWith(
                                color: AppTheme.textSecondaryColor,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),

            const SizedBox(height: 16),

            // Sort and Clear Bar
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
                decoration: AppTheme.cardDecoration,
                child: Row(
                  children: [
                    // Clear Record Icon (Left)
                    IconButton(
                      onPressed: _showClearRecordDialog,
                      icon: Icon(
                        MdiIcons.deleteEmpty,
                        color: AppTheme.errorColor,
                      ),
                      tooltip: 'مسح السجل',
                    ),

                    const Spacer(),

                    // Sort Icon (Right)
                    IconButton(
                      onPressed: _showSortDialog,
                      icon: Icon(MdiIcons.sort, color: AppTheme.primaryColor),
                      tooltip: 'ترتيب',
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Transactions List
            Expanded(
              child: Consumer<TransactionProvider>(
                builder: (context, transactionProvider, child) {
                  if (transactionProvider.isLoading) {
                    return const Center(child: CircularProgressIndicator());
                  }

                  if (transactionProvider.transactions.isEmpty) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            MdiIcons.receiptText,
                            size: 64,
                            color: AppTheme.textSecondaryColor,
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'لا توجد معاملات',
                            style: AppTheme.headingSmall.copyWith(
                              color: AppTheme.textSecondaryColor,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'اضغط على زر الإضافة لإضافة معاملة جديدة',
                            style: AppTheme.bodyMedium.copyWith(
                              color: AppTheme.textSecondaryColor,
                            ),
                          ),
                        ],
                      ),
                    );
                  }

                  return RefreshIndicator(
                    onRefresh: () => _loadTransactions(),
                    child: ListView.builder(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      itemCount: transactionProvider.transactions.length,
                      itemBuilder: (context, index) {
                        final transaction =
                            transactionProvider.transactions[index];
                        return Padding(
                          padding: const EdgeInsets.only(bottom: 12),
                          child: TransactionCard(
                            transaction: transaction,
                            onTap: () => _editTransaction(transaction),
                            onDelete: () => _deleteTransaction(transaction),
                          ),
                        );
                      },
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),

      // Add Transaction Button
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder:
                  (context) => AddTransactionScreen(customer: widget.customer),
            ),
          ).then((_) {
            // Refresh data when returning
            _loadTransactions();
            Provider.of<CustomerProvider>(
              context,
              listen: false,
            ).refreshCustomer(widget.customer.id!);
          });
        },
        child: const Icon(Icons.add),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.startFloat,
    );
  }

  void _showSortDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('ترتيب حسب'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                RadioListTile<String>(
                  title: const Text('التاريخ'),
                  value: 'date',
                  groupValue: _selectedSort,
                  onChanged: (value) {
                    setState(() {
                      _selectedSort = value!;
                    });
                    Provider.of<TransactionProvider>(
                      context,
                      listen: false,
                    ).sortTransactions(_selectedSort);
                    Navigator.pop(context);
                  },
                ),
                RadioListTile<String>(
                  title: const Text('المبلغ'),
                  value: 'amount',
                  groupValue: _selectedSort,
                  onChanged: (value) {
                    setState(() {
                      _selectedSort = value!;
                    });
                    Provider.of<TransactionProvider>(
                      context,
                      listen: false,
                    ).sortTransactions(_selectedSort);
                    Navigator.pop(context);
                  },
                ),
                RadioListTile<String>(
                  title: const Text('النوع'),
                  value: 'type',
                  groupValue: _selectedSort,
                  onChanged: (value) {
                    setState(() {
                      _selectedSort = value!;
                    });
                    Provider.of<TransactionProvider>(
                      context,
                      listen: false,
                    ).sortTransactions(_selectedSort);
                    Navigator.pop(context);
                  },
                ),
              ],
            ),
          ),
    );
  }

  void _showClearRecordDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('تأكيد المسح'),
            content: const Text(
              'هل أنت متأكد من مسح جميع المعاملات؟ لا يمكن التراجع عن هذا الإجراء.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
              TextButton(
                onPressed: () async {
                  Navigator.pop(context);
                  await _clearAllTransactions();
                },
                child: Text(
                  'مسح',
                  style: TextStyle(color: AppTheme.errorColor),
                ),
              ),
            ],
          ),
    );
  }

  Future<void> _clearAllTransactions() async {
    try {
      final transactionProvider = Provider.of<TransactionProvider>(
        context,
        listen: false,
      );
      await transactionProvider.clearAllTransactions(widget.customer.id!);

      // Refresh customer data
      await Provider.of<CustomerProvider>(
        context,
        listen: false,
      ).refreshCustomer(widget.customer.id!);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم مسح جميع المعاملات بنجاح'),
            backgroundColor: AppTheme.successColor,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ: ${e.toString()}'),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    }
  }

  void _editTransaction(DebtTransaction transaction) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder:
            (context) => AddTransactionScreen(
              customer: widget.customer,
              editTransaction: transaction,
            ),
      ),
    ).then((_) {
      // Refresh data when returning
      _loadTransactions();
      Provider.of<CustomerProvider>(
        context,
        listen: false,
      ).refreshCustomer(widget.customer.id!);
    });
  }

  void _deleteTransaction(DebtTransaction transaction) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('تأكيد الحذف'),
            content: const Text('هل أنت متأكد من حذف هذه المعاملة؟'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
              TextButton(
                onPressed: () async {
                  Navigator.pop(context);
                  try {
                    final transactionProvider =
                        Provider.of<TransactionProvider>(
                          context,
                          listen: false,
                        );
                    await transactionProvider.deleteTransaction(
                      transaction.id!,
                      widget.customer.id!,
                    );

                    // Refresh customer data
                    await Provider.of<CustomerProvider>(
                      context,
                      listen: false,
                    ).refreshCustomer(widget.customer.id!);

                    if (mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('تم حذف المعاملة بنجاح'),
                          backgroundColor: AppTheme.successColor,
                        ),
                      );
                    }
                  } catch (e) {
                    if (mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('حدث خطأ: ${e.toString()}'),
                          backgroundColor: AppTheme.errorColor,
                        ),
                      );
                    }
                  }
                },
                child: Text(
                  'حذف',
                  style: TextStyle(color: AppTheme.errorColor),
                ),
              ),
            ],
          ),
    );
  }

  void _onPrintPressed() {
    // TODO: Implement print functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('ميزة الطباعة ستكون متاحة قريباً')),
    );
  }

  void _onCallPressed() async {
    if (widget.customer.phone != null && widget.customer.phone!.isNotEmpty) {
      final Uri phoneUri = Uri(scheme: 'tel', path: widget.customer.phone);
      try {
        await launchUrl(phoneUri);
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('لا يمكن إجراء المكالمة'),
              backgroundColor: AppTheme.errorColor,
            ),
          );
        }
      }
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('رقم الهاتف غير متوفر'),
          backgroundColor: AppTheme.warningColor,
        ),
      );
    }
  }
}
