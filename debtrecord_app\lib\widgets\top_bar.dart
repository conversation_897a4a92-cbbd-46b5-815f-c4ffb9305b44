import 'package:flutter/material.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import '../utils/app_theme.dart';

class TopBar extends StatelessWidget {
  final TextEditingController searchController;
  final Function(String) onSearchChanged;
  final VoidCallback onSettingsPressed;
  final VoidCallback onBackupPressed;
  final VoidCallback onNotificationsPressed;

  const TopBar({
    super.key,
    required this.searchController,
    required this.onSearchChanged,
    required this.onSettingsPressed,
    required this.onBackupPressed,
    required this.onNotificationsPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.surfaceColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Left side icons (Settings, Backup, Notifications)
          Row(
            children: [
              IconButton(
                onPressed: onSettingsPressed,
                icon: Icon(MdiIcons.cog, color: AppTheme.primaryColor),
                tooltip: 'الإعدادات',
              ),
              const SizedBox(width: 8),
              IconButton(
                onPressed: onBackupPressed,
                icon: Icon(MdiIcons.cloudUpload, color: AppTheme.primaryColor),
                tooltip: 'النسخ الاحتياطي',
              ),
              const SizedBox(width: 8),
              IconButton(
                onPressed: onNotificationsPressed,
                icon: Icon(MdiIcons.bell, color: AppTheme.primaryColor),
                tooltip: 'الإشعارات',
              ),
            ],
          ),

          const SizedBox(width: 16),

          // Right side search box
          Expanded(
            child: Container(
              height: 45,
              decoration: AppTheme.searchBoxDecoration,
              child: TextField(
                controller: searchController,
                onChanged: onSearchChanged,
                textDirection: TextDirection.rtl,
                textAlign: TextAlign.right,
                style: AppTheme.bodyMedium,
                decoration: InputDecoration(
                  hintText: 'البحث عن عميل...',
                  hintStyle: AppTheme.bodyMedium.copyWith(
                    color: AppTheme.textSecondaryColor,
                  ),
                  prefixIcon: Icon(
                    MdiIcons.magnify,
                    color: AppTheme.primaryColor,
                  ),
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
