import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import 'package:image_picker/image_picker.dart';
import '../providers/settings_provider.dart';
import '../utils/app_theme.dart';
import '../services/print_service.dart';
import '../services/backup_service.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  final _companyNameController = TextEditingController();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final settingsProvider = Provider.of<SettingsProvider>(
        context,
        listen: false,
      );
      _companyNameController.text = settingsProvider.companyName;
    });
  }

  @override
  void dispose() {
    _companyNameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: const Text('الإعدادات'),
        backgroundColor: AppTheme.surfaceColor,
        elevation: 0,
        leading: IconButton(
          onPressed: () => Navigator.pop(context),
          icon: const Icon(Icons.arrow_back),
        ),
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Consumer<SettingsProvider>(
            builder: (context, settingsProvider, child) {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // Backup & Restore Section
                  _buildSectionCard(
                    title: 'النسخ الاحتياطي والاستعادة',
                    icon: MdiIcons.cloudUpload,
                    children: [
                      _buildSettingTile(
                        title: 'إنشاء نسخة احتياطية',
                        subtitle: 'حفظ البيانات في ملف',
                        icon: MdiIcons.cloudUpload,
                        onTap: _createBackup,
                      ),
                      const Divider(),
                      _buildSettingTile(
                        title: 'استعادة النسخة الاحتياطية',
                        subtitle: 'استعادة البيانات من ملف',
                        icon: MdiIcons.cloudDownload,
                        onTap: _restoreBackup,
                      ),
                      const Divider(),
                      _buildSettingTile(
                        title: 'طباعة',
                        subtitle: 'طباعة البيانات',
                        icon: MdiIcons.printer,
                        onTap: _printData,
                      ),
                    ],
                  ),

                  const SizedBox(height: 16),

                  // Print Settings Section
                  _buildSectionCard(
                    title: 'إعدادات الطباعة',
                    icon: MdiIcons.printer,
                    children: [
                      _buildSettingTile(
                        title: 'اسم الشركة',
                        subtitle: settingsProvider.companyName,
                        icon: MdiIcons.domain,
                        onTap: _editCompanyName,
                      ),
                      const Divider(),
                      _buildSettingTile(
                        title: 'شعار الشركة',
                        subtitle:
                            settingsProvider.companyLogo.isEmpty
                                ? 'لم يتم تحديد شعار'
                                : 'تم تحديد الشعار',
                        icon: MdiIcons.image,
                        onTap: _selectLogo,
                      ),
                      const Divider(),
                      _buildSettingTile(
                        title: 'طباعة',
                        subtitle: 'طباعة البيانات',
                        icon: MdiIcons.printer,
                        onTap: _printData,
                      ),
                      const Divider(),
                      _buildDropdownTile(
                        title: 'حجم الطباعة',
                        value: settingsProvider.printerSize,
                        items: settingsProvider.printerSizes,
                        onChanged: (value) {
                          settingsProvider.setPrinterSize(value!);
                        },
                        icon: MdiIcons.printerSettings,
                      ),
                    ],
                  ),

                  const SizedBox(height: 16),

                  // Currency Settings Section
                  _buildSectionCard(
                    title: 'إعدادات العملة',
                    icon: MdiIcons.currencyUsd,
                    children: [
                      _buildDropdownTile(
                        title: 'العملة الأساسية',
                        value: settingsProvider.currency,
                        items: settingsProvider.currencies.keys.toList(),
                        onChanged: (value) {
                          settingsProvider.setCurrency(value!);
                        },
                        icon: MdiIcons.currencyUsd,
                        displayValue:
                            '${settingsProvider.currencies[settingsProvider.currency]} - $settingsProvider.currency',
                      ),
                    ],
                  ),

                  const SizedBox(height: 16),

                  // App Settings Section
                  _buildSectionCard(
                    title: 'إعدادات التطبيق',
                    icon: MdiIcons.cog,
                    children: [
                      _buildSwitchTile(
                        title: 'إخفاء المبالغ',
                        subtitle: 'إخفاء المبالغ المالية في الواجهة الرئيسية',
                        value: settingsProvider.hideAmounts,
                        onChanged: (value) {
                          settingsProvider.setHideAmounts(value);
                        },
                        icon: MdiIcons.eyeOff,
                      ),
                    ],
                  ),

                  const SizedBox(height: 16),

                  // Support & About Section
                  _buildSectionCard(
                    title: 'الدعم والمعلومات',
                    icon: MdiIcons.helpCircle,
                    children: [
                      _buildSettingTile(
                        title: 'دعم التطبيق',
                        subtitle: 'تواصل مع فريق الدعم',
                        icon: MdiIcons.lifebuoy,
                        onTap: _showSupport,
                      ),
                      const Divider(),
                      _buildSettingTile(
                        title: 'حول التطبيق',
                        subtitle: 'معلومات التطبيق والإصدار',
                        icon: MdiIcons.information,
                        onTap: _showAbout,
                      ),
                    ],
                  ),

                  const SizedBox(height: 32),
                ],
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildSectionCard({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Container(
      decoration: AppTheme.cardDecoration,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Icon(icon, color: AppTheme.primaryColor),
                const SizedBox(width: 12),
                Text(title, style: AppTheme.headingSmall),
              ],
            ),
          ),
          ...children,
        ],
      ),
    );
  }

  Widget _buildSettingTile({
    required String title,
    required String subtitle,
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Icon(icon, color: AppTheme.primaryColor),
      title: Text(title, style: AppTheme.bodyLarge),
      subtitle: Text(subtitle, style: AppTheme.bodySmall),
      trailing: Icon(
        Icons.arrow_forward_ios,
        size: 16,
        color: AppTheme.textSecondaryColor,
      ),
      onTap: onTap,
    );
  }

  Widget _buildDropdownTile({
    required String title,
    required String value,
    required List<String> items,
    required Function(String?) onChanged,
    required IconData icon,
    String? displayValue,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          Icon(icon, color: AppTheme.primaryColor),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(title, style: AppTheme.bodyLarge),
                const SizedBox(height: 4),
                DropdownButton<String>(
                  value: value,
                  isExpanded: true,
                  underline: Container(),
                  items:
                      items.map((String item) {
                        return DropdownMenuItem<String>(
                          value: item,
                          child: Text(
                            displayValue != null && item == value
                                ? displayValue
                                : item,
                            style: AppTheme.bodyMedium,
                          ),
                        );
                      }).toList(),
                  onChanged: onChanged,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSwitchTile({
    required String title,
    required String subtitle,
    required bool value,
    required Function(bool) onChanged,
    required IconData icon,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          Icon(icon, color: AppTheme.primaryColor),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(title, style: AppTheme.bodyLarge),
                Text(subtitle, style: AppTheme.bodySmall),
              ],
            ),
          ),
          Switch(
            value: value,
            onChanged: onChanged,
            activeColor: AppTheme.primaryColor,
          ),
        ],
      ),
    );
  }

  void _editCompanyName() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('تعديل اسم الشركة'),
            content: TextField(
              controller: _companyNameController,
              decoration: const InputDecoration(
                labelText: 'اسم الشركة',
                hintText: 'أدخل اسم الشركة',
              ),
              textAlign: TextAlign.right,
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
              TextButton(
                onPressed: () {
                  final settingsProvider = Provider.of<SettingsProvider>(
                    context,
                    listen: false,
                  );
                  settingsProvider.setCompanyName(_companyNameController.text);
                  Navigator.pop(context);
                },
                child: const Text('حفظ'),
              ),
            ],
          ),
    );
  }

  void _selectLogo() async {
    final settingsProvider = Provider.of<SettingsProvider>(
      context,
      listen: false,
    );
    final image = await ImagePicker().pickImage(source: ImageSource.gallery);
    if (image != null) {
      settingsProvider.setCompanyLogo(image.path);
      setState(() {}); // Update the UI when a logo is selected
    }
  }

  void _createBackup() async {
    final backupService = BackupService();
    final success = await backupService.createBackup();
    if (success) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('تم إنشاء النسخة الاحتياطية بنجاح')),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('فشل في إنشاء النسخة الاحتياطية')),
      );
    }
  }

  void _printData() async {
    final printService = PrintService();
    final success = await printService.printData();
    if (success) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('تم الطباعة بنجاح')));
    } else {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('فشل في الطباعة')));
    }
  }

  void _restoreBackup() async {
    final backupService = BackupService();
    final success = await backupService.restoreBackup();
    if (success) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('تم استعادة النسخة الاحتياطية بنجاح')),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('فشل في استعادة النسخة الاحتياطية')),
      );
    }
  }

  void _printData() async {
    final printService = PrintService();
    final success = await printService.printData();
    if (success) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('تم الطباعة بنجاح')));
    } else {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('فشل في الطباعة')));
    }
  }

  void _showSupport() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('دعم التطبيق'),
            content: const Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('للحصول على الدعم، يرجى التواصل معنا:'),
                SizedBox(height: 16),
                Text('البريد الإلكتروني: <EMAIL>'),
                Text('الهاتف: +964 XXX XXX XXXX'),
                Text('الموقع الإلكتروني: www.debtrecord.com'),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('موافق'),
              ),
            ],
          ),
    );
  }

  void _showAbout() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('حول التطبيق'),
            content: const Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('تطبيق إدارة الديون'),
                Text('الإصدار: 1.0.0'),
                SizedBox(height: 16),
                Text('تطبيق شامل لإدارة الديون والمعاملات المالية مع العملاء'),
                SizedBox(height: 16),
                Text('المطور: فريق تطوير التطبيقات'),
                Text('حقوق الطبع والنشر © 2024'),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('موافق'),
              ),
            ],
          ),
    );
  }
}
