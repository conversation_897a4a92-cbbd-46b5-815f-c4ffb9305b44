import 'package:flutter/foundation.dart';
import '../database/database_helper.dart';
import '../models/transaction.dart';
import '../services/backup_service.dart';

class TransactionProvider with ChangeNotifier {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  List<DebtTransaction> _transactions = [];
  bool _isLoading = false;
  int? _currentCustomerId;

  List<DebtTransaction> get transactions => _transactions;
  bool get isLoading => _isLoading;
  int? get currentCustomerId => _currentCustomerId;

  Future<void> loadTransactions(int customerId) async {
    _isLoading = true;
    _currentCustomerId = customerId;
    notifyListeners();

    try {
      _transactions = await _databaseHelper.getCustomerTransactions(customerId);
    } catch (e) {
      debugPrint('Error loading transactions: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> addTransaction(DebtTransaction transaction) async {
    try {
      final id = await _databaseHelper.insertTransaction(transaction);
      final newTransaction = transaction.copyWith(id: id);
      _transactions.insert(0, newTransaction);

      // تحديث فوري للواجهة
      notifyListeners();

      // إنشاء نسخة احتياطية تلقائية
      BackupService.createAutoBackup();
    } catch (e) {
      debugPrint('Error adding transaction: $e');
      rethrow;
    }
  }

  Future<void> updateTransaction(DebtTransaction transaction) async {
    try {
      await _databaseHelper.updateTransaction(transaction);
      final index = _transactions.indexWhere((t) => t.id == transaction.id);
      if (index != -1) {
        _transactions[index] = transaction;

        // تحديث فوري للواجهة
        notifyListeners();

        // إنشاء نسخة احتياطية تلقائية
        BackupService.createAutoBackup();
      }
    } catch (e) {
      debugPrint('Error updating transaction: $e');
      rethrow;
    }
  }

  Future<void> deleteTransaction(int transactionId, int customerId) async {
    try {
      await _databaseHelper.deleteTransaction(transactionId, customerId);
      _transactions.removeWhere((t) => t.id == transactionId);

      // تحديث فوري للواجهة
      notifyListeners();

      // إنشاء نسخة احتياطية تلقائية
      BackupService.createAutoBackup();
    } catch (e) {
      debugPrint('Error deleting transaction: $e');
      rethrow;
    }
  }

  void sortTransactions(String sortBy) {
    switch (sortBy) {
      case 'date':
        _transactions.sort((a, b) {
          final dateComparison = b.date.compareTo(a.date);
          if (dateComparison != 0) return dateComparison;
          return b.time.compareTo(a.time);
        });
        break;
      case 'amount':
        _transactions.sort((a, b) => b.amount.compareTo(a.amount));
        break;
      case 'type':
        _transactions.sort((a, b) => a.type.name.compareTo(b.type.name));
        break;
    }
    notifyListeners();
  }

  double get totalDebt {
    return _transactions
        .where((t) => t.type == TransactionType.debt)
        .fold(0.0, (sum, t) => sum + t.amount);
  }

  double get totalPayments {
    return _transactions
        .where((t) => t.type == TransactionType.payment)
        .fold(0.0, (sum, t) => sum + t.amount);
  }

  double get remainingAmount => totalDebt - totalPayments;

  void clearTransactions() {
    _transactions.clear();
    _currentCustomerId = null;
    notifyListeners();
  }

  Future<void> clearAllTransactions(int customerId) async {
    try {
      for (final transaction in _transactions) {
        if (transaction.id != null) {
          await _databaseHelper.deleteTransaction(transaction.id!, customerId);
        }
      }
      _transactions.clear();
      notifyListeners();
    } catch (e) {
      debugPrint('Error clearing all transactions: $e');
      rethrow;
    }
  }
}
