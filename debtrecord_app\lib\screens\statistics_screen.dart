import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import '../providers/customer_provider.dart';
import '../providers/settings_provider.dart';
import '../utils/app_theme.dart';
import '../widgets/stat_card.dart';

class StatisticsScreen extends StatefulWidget {
  const StatisticsScreen({super.key});

  @override
  State<StatisticsScreen> createState() => _StatisticsScreenState();
}

class _StatisticsScreenState extends State<StatisticsScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<CustomerProvider>(context, listen: false).loadCustomers();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: const Text('الإحصائيات والتقارير'),
        backgroundColor: AppTheme.surfaceColor,
        elevation: 0,
        leading: IconButton(
          onPressed: () => Navigator.pop(context),
          icon: const Icon(Icons.arrow_back),
        ),
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Consumer2<CustomerProvider, SettingsProvider>(
            builder: (context, customerProvider, settingsProvider, child) {
              final customers = customerProvider.customers;
              final totalCustomers = customers.length;
              final totalDebt = customers.fold(
                0.0,
                (sum, c) => sum + c.totalDebt,
              );
              final totalPaid = customers.fold(
                0.0,
                (sum, c) => sum + c.totalPaid,
              );
              final totalRemaining = customers.fold(
                0.0,
                (sum, c) => sum + c.remainingAmount,
              );
              final activeCustomers =
                  customers.where((c) => c.remainingAmount > 0).length;
              final settledCustomers =
                  customers.where((c) => c.remainingAmount <= 0).length;

              return Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // Overview Cards
                  Row(
                    children: [
                      Expanded(
                        child: StatCard(
                          title: 'إجمالي العملاء',
                          value: totalCustomers.toString(),
                          icon: MdiIcons.accountGroup,
                          color: AppTheme.primaryColor,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: StatCard(
                          title: 'عملاء نشطون',
                          value: activeCustomers.toString(),
                          icon: MdiIcons.accountCheck,
                          color: AppTheme.warningColor,
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 12),

                  Row(
                    children: [
                      Expanded(
                        child: StatCard(
                          title: 'عملاء مسددون',
                          value: settledCustomers.toString(),
                          icon: MdiIcons.accountCheckOutline,
                          color: AppTheme.successColor,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: StatCard(
                          title: 'معدل التسديد',
                          value:
                              totalDebt > 0
                                  ? '${((totalPaid / totalDebt) * 100).toStringAsFixed(1)}%'
                                  : '0%',
                          icon: MdiIcons.percent,
                          color: AppTheme.secondaryColor,
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 24),

                  // Financial Overview
                  Container(
                    padding: const EdgeInsets.all(20),
                    decoration: AppTheme.cardDecoration,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              MdiIcons.chartLine,
                              color: AppTheme.primaryColor,
                            ),
                            const SizedBox(width: 12),
                            const Text(
                              'النظرة المالية العامة',
                              style: AppTheme.headingSmall,
                            ),
                          ],
                        ),
                        const SizedBox(height: 20),

                        _buildFinancialRow(
                          'إجمالي الديون',
                          settingsProvider.formatAmount(totalDebt),
                          AppTheme.errorColor,
                        ),
                        const SizedBox(height: 12),
                        _buildFinancialRow(
                          'إجمالي المدفوع',
                          settingsProvider.formatAmount(totalPaid),
                          AppTheme.successColor,
                        ),
                        const SizedBox(height: 12),
                        _buildFinancialRow(
                          'المبلغ المتبقي',
                          settingsProvider.formatAmount(totalRemaining),
                          AppTheme.warningColor,
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 24),

                  // Top Debtors
                  Container(
                    padding: const EdgeInsets.all(20),
                    decoration: AppTheme.cardDecoration,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              MdiIcons.trophyOutline,
                              color: AppTheme.primaryColor,
                            ),
                            const SizedBox(width: 12),
                            const Text(
                              'أكبر المدينين',
                              style: AppTheme.headingSmall,
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),

                        if (customers.isEmpty)
                          const Center(
                            child: Text(
                              'لا توجد بيانات',
                              style: AppTheme.bodyMedium,
                            ),
                          )
                        else
                          ...(customers
                                  .where((c) => c.remainingAmount > 0)
                                  .toList()
                                ..sort(
                                  (a, b) => b.remainingAmount.compareTo(
                                    a.remainingAmount,
                                  ),
                                ))
                              .take(5)
                              .map(
                                (customer) => Padding(
                                  padding: const EdgeInsets.only(bottom: 8),
                                  child: Row(
                                    children: [
                                      Expanded(
                                        child: Text(
                                          customer.name,
                                          style: AppTheme.bodyMedium,
                                        ),
                                      ),
                                      Text(
                                        settingsProvider.formatAmount(
                                          customer.remainingAmount,
                                        ),
                                        style: AppTheme.bodyMedium.copyWith(
                                          fontWeight: FontWeight.w600,
                                          color: AppTheme.errorColor,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 24),

                  // Quick Actions
                  Container(
                    padding: const EdgeInsets.all(20),
                    decoration: AppTheme.cardDecoration,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(MdiIcons.flash, color: AppTheme.primaryColor),
                            const SizedBox(width: 12),
                            const Text(
                              'إجراءات سريعة',
                              style: AppTheme.headingSmall,
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),

                        Row(
                          children: [
                            Expanded(
                              child: ElevatedButton.icon(
                                onPressed: _exportReport,
                                icon: Icon(MdiIcons.fileExport),
                                label: const Text('تصدير تقرير'),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: AppTheme.successColor,
                                ),
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: ElevatedButton.icon(
                                onPressed: _sendReminders,
                                icon: Icon(MdiIcons.bellRing),
                                label: const Text('إرسال تذكيرات'),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: AppTheme.warningColor,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 32),
                ],
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildFinancialRow(String label, String value, Color color) {
    return Row(
      children: [
        Expanded(child: Text(label, style: AppTheme.bodyMedium)),
        Text(
          value,
          style: AppTheme.bodyLarge.copyWith(
            fontWeight: FontWeight.w600,
            color: color,
          ),
        ),
      ],
    );
  }

  void _exportReport() {
    // TODO: Implement export functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('ميزة تصدير التقارير ستكون متاحة قريباً')),
    );
  }

  void _sendReminders() {
    // TODO: Implement reminder functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('ميزة إرسال التذكيرات ستكون متاحة قريباً')),
    );
  }
}
