import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class AppHelpers {
  // Date formatting
  static String formatDate(DateTime date) {
    return DateFormat('yyyy/MM/dd').format(date);
  }

  static String formatDateArabic(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  static String formatTime(TimeOfDay time) {
    return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
  }

  // Number formatting
  static String formatCurrency(double amount, String symbol) {
    String formattedAmount = amount.toStringAsFixed(0);

    if (formattedAmount.length > 3) {
      String result = '';
      int count = 0;
      for (int i = formattedAmount.length - 1; i >= 0; i--) {
        if (count == 3) {
          result = ',$result';
          count = 0;
        }
        result = formattedAmount[i] + result;
        count++;
      }
      formattedAmount = result;
    }

    return '$formattedAmount $symbol';
  }

  // Validation
  static bool isValidPhone(String phone) {
    if (phone.isEmpty) return true; // Optional field
    return RegExp(r'^07[0-9]{9}$').hasMatch(phone);
  }

  static bool isValidAmount(String amount) {
    if (amount.isEmpty) return false;
    final parsed = double.tryParse(amount);
    return parsed != null && parsed > 0;
  }

  // Colors for customer avatars
  static List<Color> get avatarColors => [
    const Color(0xFF3498DB), // Blue
    const Color(0xFF9B59B6), // Purple
    const Color(0xFF1ABC9C), // Turquoise
    const Color(0xFFE74C3C), // Red
    const Color(0xFFF39C12), // Orange
    const Color(0xFF2ECC71), // Green
    const Color(0xFFE67E22), // Carrot
    const Color(0xFF34495E), // Wet Asphalt
    const Color(0xFF16A085), // Green Sea
    const Color(0xFF8E44AD), // Wisteria
    const Color(0xFF2980B9), // Belize Hole
    const Color(0xFFC0392B), // Pomegranate
  ];

  static Color getColorForName(String name) {
    int hash = name.hashCode;
    return avatarColors[hash.abs() % avatarColors.length];
  }

  // Get initials from name
  static String getInitials(String name) {
    List<String> words = name.trim().split(' ');
    if (words.isEmpty) return '';

    if (words.length == 1) {
      return words[0].isNotEmpty ? words[0][0].toUpperCase() : '';
    } else {
      String firstInitial = words[0].isNotEmpty ? words[0][0] : '';
      String lastInitial =
          words[words.length - 1].isNotEmpty ? words[words.length - 1][0] : '';
      return (firstInitial + lastInitial).toUpperCase();
    }
  }

  // Show confirmation dialog
  static Future<bool> showConfirmDialog(
    BuildContext context, {
    required String title,
    required String content,
    String confirmText = 'تأكيد',
    String cancelText = 'إلغاء',
    Color? confirmColor,
  }) async {
    final result = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(title),
            content: Text(content),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context, false),
                child: Text(cancelText),
              ),
              TextButton(
                onPressed: () => Navigator.pop(context, true),
                child: Text(confirmText, style: TextStyle(color: confirmColor)),
              ),
            ],
          ),
    );
    return result ?? false;
  }

  // Show success snackbar
  static void showSuccessSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: const Color(0xFF27AE60),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  // Show error snackbar
  static void showErrorSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: const Color(0xFFE74C3C),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  // Show warning snackbar
  static void showWarningSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: const Color(0xFFF39C12),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  // Generate random transaction reference
  static String generateTransactionRef() {
    final now = DateTime.now();
    final timestamp = now.millisecondsSinceEpoch.toString();
    return 'TXN${timestamp.substring(timestamp.length - 8)}';
  }

  // Calculate percentage
  static double calculatePercentage(double part, double total) {
    if (total == 0) return 0;
    return (part / total) * 100;
  }

  // Get relative time (e.g., "منذ ساعتين")
  static String getRelativeTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return 'منذ ${difference.inDays} ${difference.inDays == 1 ? 'يوم' : 'أيام'}';
    } else if (difference.inHours > 0) {
      return 'منذ ${difference.inHours} ${difference.inHours == 1 ? 'ساعة' : 'ساعات'}';
    } else if (difference.inMinutes > 0) {
      return 'منذ ${difference.inMinutes} ${difference.inMinutes == 1 ? 'دقيقة' : 'دقائق'}';
    } else {
      return 'الآن';
    }
  }

  // Convert Arabic numerals to English
  static String arabicToEnglishNumbers(String input) {
    const arabicNumbers = '٠١٢٣٤٥٦٧٨٩';
    const englishNumbers = '0123456789';

    String result = input;
    for (int i = 0; i < arabicNumbers.length; i++) {
      result = result.replaceAll(arabicNumbers[i], englishNumbers[i]);
    }
    return result;
  }

  // Convert English numerals to Arabic
  static String englishToArabicNumbers(String input) {
    const arabicNumbers = '٠١٢٣٤٥٦٧٨٩';
    const englishNumbers = '0123456789';

    String result = input;
    for (int i = 0; i < englishNumbers.length; i++) {
      result = result.replaceAll(englishNumbers[i], arabicNumbers[i]);
    }
    return result;
  }
}
