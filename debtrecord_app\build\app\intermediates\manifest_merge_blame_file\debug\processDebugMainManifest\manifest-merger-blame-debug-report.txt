1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.debtrecord_app"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->C:\Users\<USER>\Desktop\debt_record\debtrecord_app\android\app\src\debug\AndroidManifest.xml:6:5-66
15-->C:\Users\<USER>\Desktop\debt_record\debtrecord_app\android\app\src\debug\AndroidManifest.xml:6:22-64
16    <!--
17     Required to query activities that can process text, see:
18         https://developer.android.com/training/package-visibility and
19         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
20
21         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
22    -->
23    <queries>
23-->C:\Users\<USER>\Desktop\debt_record\debtrecord_app\android\app\src\main\AndroidManifest.xml:39:5-44:15
24        <intent>
24-->C:\Users\<USER>\Desktop\debt_record\debtrecord_app\android\app\src\main\AndroidManifest.xml:40:9-43:18
25            <action android:name="android.intent.action.PROCESS_TEXT" />
25-->C:\Users\<USER>\Desktop\debt_record\debtrecord_app\android\app\src\main\AndroidManifest.xml:41:13-72
25-->C:\Users\<USER>\Desktop\debt_record\debtrecord_app\android\app\src\main\AndroidManifest.xml:41:21-70
26
27            <data android:mimeType="text/plain" />
27-->C:\Users\<USER>\Desktop\debt_record\debtrecord_app\android\app\src\main\AndroidManifest.xml:42:13-50
27-->C:\Users\<USER>\Desktop\debt_record\debtrecord_app\android\app\src\main\AndroidManifest.xml:42:19-48
28        </intent>
29        <intent>
29-->[:file_picker] C:\Users\<USER>\Desktop\debt_record\debtrecord_app\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:18
30            <action android:name="android.intent.action.GET_CONTENT" />
30-->[:file_picker] C:\Users\<USER>\Desktop\debt_record\debtrecord_app\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-72
30-->[:file_picker] C:\Users\<USER>\Desktop\debt_record\debtrecord_app\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:21-69
31
32            <data android:mimeType="*/*" />
32-->C:\Users\<USER>\Desktop\debt_record\debtrecord_app\android\app\src\main\AndroidManifest.xml:42:13-50
32-->C:\Users\<USER>\Desktop\debt_record\debtrecord_app\android\app\src\main\AndroidManifest.xml:42:19-48
33        </intent>
34    </queries>
35
36    <uses-permission android:name="android.permission.VIBRATE" />
36-->[:flutter_local_notifications] C:\Users\<USER>\Desktop\debt_record\debtrecord_app\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-66
36-->[:flutter_local_notifications] C:\Users\<USER>\Desktop\debt_record\debtrecord_app\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-63
37    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
37-->[:flutter_local_notifications] C:\Users\<USER>\Desktop\debt_record\debtrecord_app\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-77
37-->[:flutter_local_notifications] C:\Users\<USER>\Desktop\debt_record\debtrecord_app\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-74
38
39    <permission
39-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
40        android:name="com.example.debtrecord_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
40-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
41        android:protectionLevel="signature" />
41-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
42
43    <uses-permission android:name="com.example.debtrecord_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
43-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
43-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
44
45    <application
46        android:name="android.app.Application"
47        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
47-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
48        android:debuggable="true"
49        android:extractNativeLibs="true"
50        android:icon="@mipmap/ic_launcher"
51        android:label="debtrecord_app" >
52        <activity
53            android:name="com.example.debtrecord_app.MainActivity"
54            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
55            android:exported="true"
56            android:hardwareAccelerated="true"
57            android:launchMode="singleTop"
58            android:taskAffinity=""
59            android:theme="@style/LaunchTheme"
60            android:windowSoftInputMode="adjustResize" >
61
62            <!--
63                 Specifies an Android theme to apply to this Activity as soon as
64                 the Android process has started. This theme is visible to the user
65                 while the Flutter UI initializes. After that, this theme continues
66                 to determine the Window background behind the Flutter UI.
67            -->
68            <meta-data
69                android:name="io.flutter.embedding.android.NormalTheme"
70                android:resource="@style/NormalTheme" />
71
72            <intent-filter>
73                <action android:name="android.intent.action.MAIN" />
74
75                <category android:name="android.intent.category.LAUNCHER" />
76            </intent-filter>
77        </activity>
78        <!--
79             Don't delete the meta-data below.
80             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
81        -->
82        <meta-data
83            android:name="flutterEmbedding"
84            android:value="2" />
85
86        <provider
86-->[:image_picker_android] C:\Users\<USER>\Desktop\debt_record\debtrecord_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-17:20
87            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
87-->[:image_picker_android] C:\Users\<USER>\Desktop\debt_record\debtrecord_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-82
88            android:authorities="com.example.debtrecord_app.flutter.image_provider"
88-->[:image_picker_android] C:\Users\<USER>\Desktop\debt_record\debtrecord_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-74
89            android:exported="false"
89-->[:image_picker_android] C:\Users\<USER>\Desktop\debt_record\debtrecord_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-37
90            android:grantUriPermissions="true" >
90-->[:image_picker_android] C:\Users\<USER>\Desktop\debt_record\debtrecord_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-47
91            <meta-data
91-->[:image_picker_android] C:\Users\<USER>\Desktop\debt_record\debtrecord_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-16:75
92                android:name="android.support.FILE_PROVIDER_PATHS"
92-->[:image_picker_android] C:\Users\<USER>\Desktop\debt_record\debtrecord_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-67
93                android:resource="@xml/flutter_image_picker_file_paths" />
93-->[:image_picker_android] C:\Users\<USER>\Desktop\debt_record\debtrecord_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:17-72
94        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
95        <service
95-->[:image_picker_android] C:\Users\<USER>\Desktop\debt_record\debtrecord_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-31:19
96            android:name="com.google.android.gms.metadata.ModuleDependencies"
96-->[:image_picker_android] C:\Users\<USER>\Desktop\debt_record\debtrecord_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-78
97            android:enabled="false"
97-->[:image_picker_android] C:\Users\<USER>\Desktop\debt_record\debtrecord_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-36
98            android:exported="false" >
98-->[:image_picker_android] C:\Users\<USER>\Desktop\debt_record\debtrecord_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-37
99            <intent-filter>
99-->[:image_picker_android] C:\Users\<USER>\Desktop\debt_record\debtrecord_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-26:29
100                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
100-->[:image_picker_android] C:\Users\<USER>\Desktop\debt_record\debtrecord_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:17-94
100-->[:image_picker_android] C:\Users\<USER>\Desktop\debt_record\debtrecord_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:25-91
101            </intent-filter>
102
103            <meta-data
103-->[:image_picker_android] C:\Users\<USER>\Desktop\debt_record\debtrecord_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-30:36
104                android:name="photopicker_activity:0:required"
104-->[:image_picker_android] C:\Users\<USER>\Desktop\debt_record\debtrecord_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-63
105                android:value="" />
105-->[:image_picker_android] C:\Users\<USER>\Desktop\debt_record\debtrecord_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-33
106        </service>
107
108        <provider
108-->[:printing] C:\Users\<USER>\Desktop\debt_record\debtrecord_app\build\printing\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-16:20
109            android:name="net.nfet.flutter.printing.PrintFileProvider"
109-->[:printing] C:\Users\<USER>\Desktop\debt_record\debtrecord_app\build\printing\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-71
110            android:authorities="com.example.debtrecord_app.flutter.printing"
110-->[:printing] C:\Users\<USER>\Desktop\debt_record\debtrecord_app\build\printing\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-68
111            android:exported="false"
111-->[:printing] C:\Users\<USER>\Desktop\debt_record\debtrecord_app\build\printing\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
112            android:grantUriPermissions="true" >
112-->[:printing] C:\Users\<USER>\Desktop\debt_record\debtrecord_app\build\printing\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-47
113            <meta-data
113-->[:image_picker_android] C:\Users\<USER>\Desktop\debt_record\debtrecord_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-16:75
114                android:name="android.support.FILE_PROVIDER_PATHS"
114-->[:image_picker_android] C:\Users\<USER>\Desktop\debt_record\debtrecord_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-67
115                android:resource="@xml/flutter_printing_file_paths" />
115-->[:image_picker_android] C:\Users\<USER>\Desktop\debt_record\debtrecord_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:17-72
116        </provider>
117        <!--
118           Declares a provider which allows us to store files to share in
119           '.../caches/share_plus' and grant the receiving action access
120        -->
121        <provider
121-->[:share_plus] C:\Users\<USER>\Desktop\debt_record\debtrecord_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:9-21:20
122            android:name="dev.fluttercommunity.plus.share.ShareFileProvider"
122-->[:share_plus] C:\Users\<USER>\Desktop\debt_record\debtrecord_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-77
123            android:authorities="com.example.debtrecord_app.flutter.share_provider"
123-->[:share_plus] C:\Users\<USER>\Desktop\debt_record\debtrecord_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-74
124            android:exported="false"
124-->[:share_plus] C:\Users\<USER>\Desktop\debt_record\debtrecord_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-37
125            android:grantUriPermissions="true" >
125-->[:share_plus] C:\Users\<USER>\Desktop\debt_record\debtrecord_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-47
126            <meta-data
126-->[:image_picker_android] C:\Users\<USER>\Desktop\debt_record\debtrecord_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-16:75
127                android:name="android.support.FILE_PROVIDER_PATHS"
127-->[:image_picker_android] C:\Users\<USER>\Desktop\debt_record\debtrecord_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-67
128                android:resource="@xml/flutter_share_file_paths" />
128-->[:image_picker_android] C:\Users\<USER>\Desktop\debt_record\debtrecord_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:17-72
129        </provider>
130        <!--
131           This manifest declared broadcast receiver allows us to use an explicit
132           Intent when creating a PendingItent to be informed of the user's choice
133        -->
134        <receiver
134-->[:share_plus] C:\Users\<USER>\Desktop\debt_record\debtrecord_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:9-32:20
135            android:name="dev.fluttercommunity.plus.share.SharePlusPendingIntent"
135-->[:share_plus] C:\Users\<USER>\Desktop\debt_record\debtrecord_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-82
136            android:exported="false" >
136-->[:share_plus] C:\Users\<USER>\Desktop\debt_record\debtrecord_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-37
137            <intent-filter>
137-->[:share_plus] C:\Users\<USER>\Desktop\debt_record\debtrecord_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-31:29
138                <action android:name="EXTRA_CHOSEN_COMPONENT" />
138-->[:share_plus] C:\Users\<USER>\Desktop\debt_record\debtrecord_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-65
138-->[:share_plus] C:\Users\<USER>\Desktop\debt_record\debtrecord_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:25-62
139            </intent-filter>
140        </receiver>
141
142        <activity
142-->[:url_launcher_android] C:\Users\<USER>\Desktop\debt_record\debtrecord_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-11:74
143            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
143-->[:url_launcher_android] C:\Users\<USER>\Desktop\debt_record\debtrecord_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-74
144            android:exported="false"
144-->[:url_launcher_android] C:\Users\<USER>\Desktop\debt_record\debtrecord_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-37
145            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
145-->[:url_launcher_android] C:\Users\<USER>\Desktop\debt_record\debtrecord_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-71
146
147        <uses-library
147-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
148            android:name="androidx.window.extensions"
148-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
149            android:required="false" />
149-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
150        <uses-library
150-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
151            android:name="androidx.window.sidecar"
151-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
152            android:required="false" />
152-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
153
154        <provider
154-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
155            android:name="androidx.startup.InitializationProvider"
155-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
156            android:authorities="com.example.debtrecord_app.androidx-startup"
156-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
157            android:exported="false" >
157-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
158            <meta-data
158-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
159                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
159-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
160                android:value="androidx.startup" />
160-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
161            <meta-data
161-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
162                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
162-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
163                android:value="androidx.startup" />
163-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
164        </provider>
165
166        <receiver
166-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
167            android:name="androidx.profileinstaller.ProfileInstallReceiver"
167-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
168            android:directBootAware="false"
168-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
169            android:enabled="true"
169-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
170            android:exported="true"
170-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
171            android:permission="android.permission.DUMP" >
171-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
172            <intent-filter>
172-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
173                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
173-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
173-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
174            </intent-filter>
175            <intent-filter>
175-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
176                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
176-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
176-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
177            </intent-filter>
178            <intent-filter>
178-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
179                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
179-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
179-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
180            </intent-filter>
181            <intent-filter>
181-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
182                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
182-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
182-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
183            </intent-filter>
184        </receiver>
185    </application>
186
187</manifest>
