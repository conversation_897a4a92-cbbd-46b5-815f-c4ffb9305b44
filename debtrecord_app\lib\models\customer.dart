class Customer {
  final int? id;
  final String name;
  final String? address;
  final String? phone;
  final double totalDebt;
  final double totalPaid;
  final double remainingAmount;
  final DateTime createdAt;
  final DateTime updatedAt;

  Customer({
    this.id,
    required this.name,
    this.address,
    this.phone,
    this.totalDebt = 0.0,
    this.totalPaid = 0.0,
    this.remainingAmount = 0.0,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'address': address,
      'phone': phone,
      'total_debt': totalDebt,
      'total_paid': totalPaid,
      'remaining_amount': remainingAmount,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  factory Customer.fromMap(Map<String, dynamic> map) {
    return Customer(
      id: map['id']?.toInt(),
      name: map['name'] ?? '',
      address: map['address'],
      phone: map['phone'],
      totalDebt: map['total_debt']?.toDouble() ?? 0.0,
      totalPaid: map['total_paid']?.toDouble() ?? 0.0,
      remainingAmount: map['remaining_amount']?.toDouble() ?? 0.0,
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: DateTime.parse(map['updated_at']),
    );
  }

  Customer copyWith({
    int? id,
    String? name,
    String? address,
    String? phone,
    double? totalDebt,
    double? totalPaid,
    double? remainingAmount,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Customer(
      id: id ?? this.id,
      name: name ?? this.name,
      address: address ?? this.address,
      phone: phone ?? this.phone,
      totalDebt: totalDebt ?? this.totalDebt,
      totalPaid: totalPaid ?? this.totalPaid,
      remainingAmount: remainingAmount ?? this.remainingAmount,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'Customer{id: $id, name: $name, address: $address, phone: $phone, totalDebt: $totalDebt, totalPaid: $totalPaid, remainingAmount: $remainingAmount}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Customer && other.id == id;
  }

  @override
  int get hashCode {
    return id.hashCode;
  }
}
