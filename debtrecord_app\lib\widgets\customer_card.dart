import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/customer.dart';
import '../providers/settings_provider.dart';
import '../utils/app_theme.dart';
import 'dart:math';

class CustomerCard extends StatelessWidget {
  final Customer customer;
  final VoidCallback onTap;

  const CustomerCard({super.key, required this.customer, required this.onTap});

  @override
  Widget build(BuildContext context) {
    return Consumer<SettingsProvider>(
      builder: (context, settingsProvider, child) {
        return GestureDetector(
          onTap: onTap,
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: AppTheme.cardDecoration,
            child: Row(
              children: [
                // Left side - Amount and "المتبقي"
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      settingsProvider.formatAmount(customer.remainingAmount),
                      style: AppTheme.headingSmall.copyWith(
                        color:
                            customer.remainingAmount > 0
                                ? AppTheme.errorColor
                                : AppTheme.successColor,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'المتبقي',
                      style: AppTheme.bodySmall.copyWith(
                        color: AppTheme.textSecondaryColor,
                      ),
                    ),
                  ],
                ),

                const Spacer(),

                // Right side - Circle, Name, and Date
                Row(
                  children: [
                    // Name and Date
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(
                          customer.name,
                          style: AppTheme.headingSmall.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                          textDirection: TextDirection.rtl,
                        ),
                        const SizedBox(height: 4),
                        Text(
                          _formatDate(customer.updatedAt),
                          style: AppTheme.bodySmall.copyWith(
                            color: AppTheme.textSecondaryColor,
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(width: 12),

                    // Random colored circle
                    Container(
                      width: 50,
                      height: 50,
                      decoration: BoxDecoration(
                        color: _getRandomColor(customer.name),
                        shape: BoxShape.circle,
                      ),
                      child: Center(
                        child: Text(
                          _getInitials(customer.name),
                          style: AppTheme.bodyLarge.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  String _getInitials(String name) {
    List<String> words = name.trim().split(' ');
    if (words.isEmpty) return '';

    if (words.length == 1) {
      return words[0].isNotEmpty ? words[0][0].toUpperCase() : '';
    } else {
      String firstInitial = words[0].isNotEmpty ? words[0][0] : '';
      String lastInitial =
          words[words.length - 1].isNotEmpty ? words[words.length - 1][0] : '';
      return (firstInitial + lastInitial).toUpperCase();
    }
  }

  Color _getRandomColor(String name) {
    // Generate a consistent color based on the name
    final colors = [
      const Color(0xFF3498DB), // Blue
      const Color(0xFF9B59B6), // Purple
      const Color(0xFF1ABC9C), // Turquoise
      const Color(0xFFE74C3C), // Red
      const Color(0xFFF39C12), // Orange
      const Color(0xFF2ECC71), // Green
      const Color(0xFFE67E22), // Carrot
      const Color(0xFF34495E), // Wet Asphalt
      const Color(0xFF16A085), // Green Sea
      const Color(0xFF8E44AD), // Wisteria
      const Color(0xFF2980B9), // Belize Hole
      const Color(0xFFC0392B), // Pomegranate
    ];

    // Use hash code of name to get consistent color
    int hash = name.hashCode;
    return colors[hash.abs() % colors.length];
  }
}
