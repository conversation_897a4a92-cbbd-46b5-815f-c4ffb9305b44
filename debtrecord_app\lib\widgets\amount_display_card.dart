import 'package:flutter/material.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import '../utils/app_theme.dart';

class AmountDisplayCard extends StatelessWidget {
  final double totalAmount;
  final String currencySymbol;
  final bool hideAmounts;
  final VoidCallback onToggleVisibility;

  const AmountDisplayCard({
    super.key,
    required this.totalAmount,
    required this.currencySymbol,
    required this.hideAmounts,
    required this.onToggleVisibility,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: AppTheme.amountBoxDecoration,
      child: Row(
        children: [
          // Left side - Amount icon and hide/show toggle
          Column(
            children: [
              Icon(
                MdiIcons.currencyUsd,
                color: AppTheme.primaryColor,
                size: 32,
              ),
              const SizedBox(height: 8),
              IconButton(
                onPressed: onToggleVisibility,
                icon: Icon(
                  hideAmounts ? MdiIcons.eyeOff : MdiIcons.eye,
                  color: AppTheme.primaryColor,
                ),
                tooltip: hideAmounts ? 'إظهار المبالغ' : 'إخفاء المبالغ',
              ),
            ],
          ),

          const Spacer(),

          // Right side - Amount and label
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                _formatAmount(),
                style: AppTheme.headingLarge.copyWith(
                  color: AppTheme.primaryColor,
                  fontWeight: FontWeight.bold,
                  fontSize: 28,
                ),
                textDirection: TextDirection.rtl,
              ),
              const SizedBox(height: 4),
              Text(
                'مجموع المتبقي',
                style: AppTheme.bodyMedium.copyWith(
                  color: AppTheme.textSecondaryColor,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  String _formatAmount() {
    if (hideAmounts) {
      return '***';
    }

    // Format number with thousand separators
    String formattedAmount = totalAmount.toStringAsFixed(0);

    if (formattedAmount.length > 3) {
      String result = '';
      int count = 0;
      for (int i = formattedAmount.length - 1; i >= 0; i--) {
        if (count == 3) {
          result = ',' + result;
          count = 0;
        }
        result = formattedAmount[i] + result;
        count++;
      }
      formattedAmount = result;
    }

    return '$formattedAmount $currencySymbol';
  }
}
