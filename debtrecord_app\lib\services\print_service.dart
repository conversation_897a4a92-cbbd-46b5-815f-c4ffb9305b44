import 'dart:io';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:intl/intl.dart';
import '../models/customer.dart';
import '../models/transaction.dart';
import '../database/database_helper.dart';

class PrintService {
  static Future<void> printCustomerReport(
    Customer customer,
    List<DebtTransaction> transactions,
    String companyName,
    String? logoPath,
    bool isSmallPrint,
  ) async {
    final pdf = pw.Document();

    // حساب الإجماليات
    double totalDebt = 0;
    double totalPayment = 0;

    for (var transaction in transactions) {
      if (transaction.type == TransactionType.debt) {
        totalDebt += transaction.amount;
      } else {
        totalPayment += transaction.amount;
      }
    }

    double remaining = totalDebt - totalPayment;

    // إنشاء الصفحة
    pdf.addPage(
      pw.Page(
        pageFormat: isSmallPrint ? PdfPageFormat.a5 : PdfPageFormat.a4,
        textDirection: pw.TextDirection.rtl,
        build: (pw.Context context) {
          return pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              // رأس الصفحة
              _buildHeader(companyName, logoPath),

              pw.SizedBox(height: 20),

              // عنوان التقرير
              pw.Center(
                child: pw.Text(
                  'كشف حساب العميل',
                  style: pw.TextStyle(
                    fontSize: 18,
                    fontWeight: pw.FontWeight.bold,
                  ),
                ),
              ),

              pw.SizedBox(height: 20),

              // معلومات العميل والإجماليات
              _buildCustomerInfo(customer, totalDebt, totalPayment, remaining),

              pw.SizedBox(height: 20),

              // جدول المعاملات
              _buildTransactionsTable(transactions),

              pw.Spacer(),

              // تذييل الصفحة
              _buildFooter(),
            ],
          );
        },
      ),
    );

    // طباعة أو مشاركة
    await Printing.layoutPdf(
      onLayout: (PdfPageFormat format) async => pdf.save(),
      name: 'كشف_حساب_${customer.name}',
    );
  }

  static pw.Widget _buildHeader(String companyName, String? logoPath) {
    return pw.Row(
      mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
      children: [
        pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            pw.Text(
              companyName,
              style: pw.TextStyle(fontSize: 16, fontWeight: pw.FontWeight.bold),
            ),
            pw.Text('سجل الديون', style: pw.TextStyle(fontSize: 12)),
          ],
        ),
        if (logoPath != null && logoPath.isNotEmpty)
          pw.Container(
            width: 60,
            height: 60,
            decoration: pw.BoxDecoration(
              border: pw.Border.all(color: PdfColors.grey),
            ),
            child: _buildLogo(logoPath),
          ),
      ],
    );
  }

  static pw.Widget _buildLogo(String logoPath) {
    try {
      if (File(logoPath).existsSync()) {
        final imageBytes = File(logoPath).readAsBytesSync();
        final image = pw.MemoryImage(imageBytes);
        return pw.Image(image, fit: pw.BoxFit.contain);
      }
    } catch (e) {
      // في حالة فشل تحميل الصورة، عرض نص بديل
    }
    return pw.Center(child: pw.Text('شعار', style: pw.TextStyle(fontSize: 10)));
  }

  static pw.Widget _buildCustomerInfo(
    Customer customer,
    double totalDebt,
    double totalPayment,
    double remaining,
  ) {
    return pw.Row(
      children: [
        // معلومات العميل
        pw.Expanded(
          child: pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Text(
                'الاسم: ${customer.name}',
                style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
              ),
              pw.SizedBox(height: 5),
              pw.Text('العنوان: ${customer.address ?? 'غير محدد'}'),
              pw.SizedBox(height: 5),
              pw.Text('الهاتف: ${customer.phone ?? 'غير محدد'}'),
            ],
          ),
        ),

        // الإجماليات
        pw.Expanded(
          child: pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Text(
                'الدين الكلي: ${NumberFormat('#,##0').format(totalDebt)} د.ع',
                style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
              ),
              pw.SizedBox(height: 5),
              pw.Text(
                'التسديد الكلي: ${NumberFormat('#,##0').format(totalPayment)} د.ع',
              ),
              pw.SizedBox(height: 5),
              pw.Text(
                'المتبقي: ${NumberFormat('#,##0').format(remaining)} د.ع',
                style: pw.TextStyle(
                  fontWeight: pw.FontWeight.bold,
                  color: remaining > 0 ? PdfColors.red : PdfColors.green,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  static pw.Widget _buildTransactionsTable(List<DebtTransaction> transactions) {
    return pw.Table(
      border: pw.TableBorder.all(color: PdfColors.grey),
      children: [
        // رأس الجدول
        pw.TableRow(
          decoration: pw.BoxDecoration(color: PdfColors.grey300),
          children: [
            _buildTableCell('التاريخ', isHeader: true),
            _buildTableCell('الوقت', isHeader: true),
            _buildTableCell('الدين', isHeader: true),
            _buildTableCell('التسديد', isHeader: true),
            _buildTableCell('الملاحظات', isHeader: true),
          ],
        ),

        // صفوف البيانات
        ...transactions.map((transaction) {
          return pw.TableRow(
            children: [
              _buildTableCell(
                DateFormat('yyyy/MM/dd').format(transaction.date),
              ),
              _buildTableCell(transaction.time),
              _buildTableCell(
                transaction.type == TransactionType.debt
                    ? NumberFormat('#,##0').format(transaction.amount)
                    : '-',
              ),
              _buildTableCell(
                transaction.type == TransactionType.payment
                    ? NumberFormat('#,##0').format(transaction.amount)
                    : '-',
              ),
              _buildTableCell(transaction.notes ?? '-'),
            ],
          );
        }),
      ],
    );
  }

  static pw.Widget _buildTableCell(String text, {bool isHeader = false}) {
    return pw.Container(
      padding: pw.EdgeInsets.all(8),
      child: pw.Text(
        text,
        style: pw.TextStyle(
          fontSize: isHeader ? 12 : 10,
          fontWeight: isHeader ? pw.FontWeight.bold : pw.FontWeight.normal,
        ),
        textAlign: pw.TextAlign.center,
      ),
    );
  }

  static pw.Widget _buildFooter() {
    return pw.Row(
      mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
      children: [
        pw.Text(
          'تاريخ الطباعة: ${DateFormat('yyyy/MM/dd HH:mm').format(DateTime.now())}',
          style: pw.TextStyle(fontSize: 10),
        ),
        pw.Text('صفحة 1', style: pw.TextStyle(fontSize: 10)),
      ],
    );
  }

  static Future<void> shareCustomerReport(
    Customer customer,
    List<DebtTransaction> transactions,
    String companyName,
    String? logoPath,
    bool isSmallPrint,
  ) async {
    final pdf = pw.Document();

    // حساب الإجماليات
    double totalDebt = 0;
    double totalPayment = 0;

    for (var transaction in transactions) {
      if (transaction.type == TransactionType.debt) {
        totalDebt += transaction.amount;
      } else {
        totalPayment += transaction.amount;
      }
    }

    double remaining = totalDebt - totalPayment;

    // إنشاء الصفحة
    pdf.addPage(
      pw.Page(
        pageFormat: isSmallPrint ? PdfPageFormat.a5 : PdfPageFormat.a4,
        textDirection: pw.TextDirection.rtl,
        build: (pw.Context context) {
          return pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              _buildHeader(companyName, logoPath),
              pw.SizedBox(height: 20),
              pw.Center(
                child: pw.Text(
                  'كشف حساب العميل',
                  style: pw.TextStyle(
                    fontSize: 18,
                    fontWeight: pw.FontWeight.bold,
                  ),
                ),
              ),
              pw.SizedBox(height: 20),
              _buildCustomerInfo(customer, totalDebt, totalPayment, remaining),
              pw.SizedBox(height: 20),
              _buildTransactionsTable(transactions),
              pw.Spacer(),
              _buildFooter(),
            ],
          );
        },
      ),
    );

    final output = await getTemporaryDirectory();
    final file = File('${output.path}/كشف_حساب_${customer.name}.pdf');
    await file.writeAsBytes(await pdf.save());

    await Share.shareXFiles([
      XFile(file.path),
    ], text: 'كشف حساب العميل: ${customer.name}');
  }

  /// طباعة جميع البيانات
  static Future<bool> printAllData() async {
    try {
      final dbHelper = DatabaseHelper();
      final customers = await dbHelper.getAllCustomers();
      final transactionMaps = await dbHelper.getAllTransactions();
      final transactions =
          transactionMaps.map((map) => DebtTransaction.fromMap(map)).toList();

      final pdf = pw.Document();

      pdf.addPage(
        pw.Page(
          pageFormat: PdfPageFormat.a4,
          textDirection: pw.TextDirection.rtl,
          build: (pw.Context context) {
            return pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                _buildHeader('سجل الديون', null),
                pw.SizedBox(height: 20),
                pw.Center(
                  child: pw.Text(
                    'تقرير شامل لجميع البيانات',
                    style: pw.TextStyle(
                      fontSize: 18,
                      fontWeight: pw.FontWeight.bold,
                    ),
                  ),
                ),
                pw.SizedBox(height: 20),
                _buildAllDataSummary(customers, transactions),
                pw.SizedBox(height: 20),
                _buildAllCustomersTable(customers),
                pw.Spacer(),
                _buildFooter(),
              ],
            );
          },
        ),
      );

      await Printing.layoutPdf(
        onLayout: (PdfPageFormat format) async => pdf.save(),
        name: 'تقرير_شامل_${DateTime.now().millisecondsSinceEpoch}',
      );

      return true;
    } catch (e) {
      return false;
    }
  }

  static pw.Widget _buildAllDataSummary(
    List<Customer> customers,
    List<DebtTransaction> transactions,
  ) {
    double totalDebt = 0;
    double totalPayment = 0;

    for (var transaction in transactions) {
      if (transaction.type == TransactionType.debt) {
        totalDebt += transaction.amount;
      } else {
        totalPayment += transaction.amount;
      }
    }

    double remaining = totalDebt - totalPayment;

    return pw.Container(
      padding: pw.EdgeInsets.all(16),
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: PdfColors.grey),
        borderRadius: pw.BorderRadius.circular(8),
      ),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceAround,
        children: [
          _buildSummaryItem('عدد العملاء', customers.length.toString()),
          _buildSummaryItem(
            'إجمالي الديون',
            NumberFormat('#,##0').format(totalDebt),
          ),
          _buildSummaryItem(
            'إجمالي المدفوعات',
            NumberFormat('#,##0').format(totalPayment),
          ),
          _buildSummaryItem('المتبقي', NumberFormat('#,##0').format(remaining)),
        ],
      ),
    );
  }

  static pw.Widget _buildSummaryItem(String title, String value) {
    return pw.Column(
      children: [
        pw.Text(title, style: pw.TextStyle(fontSize: 10)),
        pw.SizedBox(height: 4),
        pw.Text(
          value,
          style: pw.TextStyle(fontSize: 12, fontWeight: pw.FontWeight.bold),
        ),
      ],
    );
  }

  static pw.Widget _buildAllCustomersTable(List<Customer> customers) {
    return pw.Table(
      border: pw.TableBorder.all(color: PdfColors.grey),
      children: [
        pw.TableRow(
          children: [
            _buildTableCell('اسم العميل', isHeader: true),
            _buildTableCell('العنوان', isHeader: true),
            _buildTableCell('الهاتف', isHeader: true),
            _buildTableCell('تاريخ الإنشاء', isHeader: true),
          ],
        ),
        ...customers.map((customer) {
          return pw.TableRow(
            children: [
              _buildTableCell(customer.name),
              _buildTableCell(customer.address ?? '-'),
              _buildTableCell(customer.phone ?? '-'),
              _buildTableCell(
                DateFormat('yyyy/MM/dd').format(customer.createdAt),
              ),
            ],
          );
        }),
      ],
    );
  }
}
