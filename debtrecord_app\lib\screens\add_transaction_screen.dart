import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import 'package:intl/intl.dart';
import '../models/customer.dart';
import '../models/transaction.dart';
import '../providers/transaction_provider.dart';
import '../providers/customer_provider.dart';
import '../utils/app_theme.dart';

class AddTransactionScreen extends StatefulWidget {
  final Customer customer;
  final bool isNewCustomer;
  final DebtTransaction? editTransaction;

  const AddTransactionScreen({
    super.key,
    required this.customer,
    this.isNewCustomer = false,
    this.editTransaction,
  });

  @override
  State<AddTransactionScreen> createState() => _AddTransactionScreenState();
}

class _AddTransactionScreenState extends State<AddTransactionScreen> {
  final _formKey = GlobalKey<FormState>();
  final _amountController = TextEditingController();
  final _notesController = TextEditingController();

  DateTime _selectedDate = DateTime.now();
  TimeOfDay _selectedTime = TimeOfDay.now();
  TransactionType _selectedType = TransactionType.debt;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    if (widget.editTransaction != null) {
      _initializeForEdit();
    }
  }

  void _initializeForEdit() {
    final transaction = widget.editTransaction!;
    _amountController.text = transaction.amount.toString();
    _notesController.text = transaction.notes ?? '';
    _selectedDate = transaction.date;
    _selectedTime = TimeOfDay(
      hour: int.parse(transaction.time.split(':')[0]),
      minute: int.parse(transaction.time.split(':')[1]),
    );
    _selectedType = transaction.type;
  }

  @override
  void dispose() {
    _amountController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: Text(
          widget.editTransaction != null
              ? 'تعديل المعاملة'
              : 'إضافة معاملة جديدة',
        ),
        backgroundColor: AppTheme.surfaceColor,
        elevation: 0,
        leading: IconButton(
          onPressed: () => Navigator.pop(context),
          icon: const Icon(Icons.arrow_back),
        ),
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Customer Info
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: AppTheme.cardDecoration,
                  child: Row(
                    children: [
                      Icon(MdiIcons.account, color: AppTheme.primaryColor),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          widget.customer.name,
                          style: AppTheme.headingSmall,
                          textAlign: TextAlign.right,
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 20),

                // Amount Field
                Container(
                  decoration: AppTheme.cardDecoration,
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Row(
                        children: [
                          Icon(
                            MdiIcons.currencyUsd,
                            color: AppTheme.primaryColor,
                          ),
                          const SizedBox(width: 8),
                          const Expanded(
                            child: Text(
                              'المبلغ *',
                              style: AppTheme.labelLarge,
                              textAlign: TextAlign.right,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      TextFormField(
                        controller: _amountController,
                        textAlign: TextAlign.left,
                        style: AppTheme.bodyMedium,
                        keyboardType: TextInputType.number,
                        decoration: const InputDecoration(hintText: '0'),
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'المبلغ مطلوب';
                          }
                          if (double.tryParse(value) == null ||
                              double.parse(value) <= 0) {
                            return 'يرجى إدخال مبلغ صحيح';
                          }
                          return null;
                        },
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 16),

                // Date and Time Row
                Row(
                  children: [
                    // Time Field (Left)
                    Expanded(
                      child: Container(
                        decoration: AppTheme.cardDecoration,
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            Row(
                              children: [
                                Icon(
                                  MdiIcons.clock,
                                  color: AppTheme.primaryColor,
                                ),
                                const SizedBox(width: 8),
                                const Expanded(
                                  child: Text(
                                    'الوقت',
                                    style: AppTheme.labelLarge,
                                    textAlign: TextAlign.right,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 12),
                            GestureDetector(
                              onTap: _selectTime,
                              child: Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 16,
                                  vertical: 12,
                                ),
                                decoration: BoxDecoration(
                                  border: Border.all(
                                    color: AppTheme.inkColor,
                                    width: 2,
                                  ),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Text(
                                  _selectedTime.format(context),
                                  style: AppTheme.bodyMedium,
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(width: 16),

                    // Date Field (Right)
                    Expanded(
                      child: Container(
                        decoration: AppTheme.cardDecoration,
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            Row(
                              children: [
                                Icon(
                                  MdiIcons.calendar,
                                  color: AppTheme.primaryColor,
                                ),
                                const SizedBox(width: 8),
                                const Expanded(
                                  child: Text(
                                    'التاريخ',
                                    style: AppTheme.labelLarge,
                                    textAlign: TextAlign.right,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 12),
                            GestureDetector(
                              onTap: _selectDate,
                              child: Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 16,
                                  vertical: 12,
                                ),
                                decoration: BoxDecoration(
                                  border: Border.all(
                                    color: AppTheme.inkColor,
                                    width: 2,
                                  ),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Text(
                                  DateFormat(
                                    'yyyy/MM/dd',
                                  ).format(_selectedDate),
                                  style: AppTheme.bodyMedium,
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 16),

                // Transaction Type Selection
                Container(
                  decoration: AppTheme.cardDecoration,
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Row(
                        children: [
                          Icon(
                            MdiIcons.swapHorizontal,
                            color: AppTheme.primaryColor,
                          ),
                          const SizedBox(width: 8),
                          const Expanded(
                            child: Text(
                              'نوع المعاملة',
                              style: AppTheme.labelLarge,
                              textAlign: TextAlign.right,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      Row(
                        children: [
                          Expanded(
                            child: RadioListTile<TransactionType>(
                              title: const Text(
                                'تسديد',
                                textAlign: TextAlign.right,
                              ),
                              value: TransactionType.payment,
                              groupValue: _selectedType,
                              onChanged: (value) {
                                setState(() {
                                  _selectedType = value!;
                                });
                              },
                              contentPadding: EdgeInsets.zero,
                            ),
                          ),
                          Expanded(
                            child: RadioListTile<TransactionType>(
                              title: const Text(
                                'دين',
                                textAlign: TextAlign.right,
                              ),
                              value: TransactionType.debt,
                              groupValue: _selectedType,
                              onChanged: (value) {
                                setState(() {
                                  _selectedType = value!;
                                });
                              },
                              contentPadding: EdgeInsets.zero,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 16),

                // Notes Field
                Container(
                  decoration: AppTheme.cardDecoration,
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Row(
                        children: [
                          Icon(MdiIcons.noteText, color: AppTheme.primaryColor),
                          const SizedBox(width: 8),
                          const Expanded(
                            child: Text(
                              'الملاحظات',
                              style: AppTheme.labelLarge,
                              textAlign: TextAlign.right,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      TextFormField(
                        controller: _notesController,
                        textAlign: TextAlign.right,
                        style: AppTheme.bodyMedium,
                        maxLines: 3,
                        decoration: const InputDecoration(
                          hintText: 'أدخل ملاحظات إضافية (اختياري)',
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 32),

                // Save Button
                SizedBox(
                  height: 50,
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _onSavePressed,
                    child:
                        _isLoading
                            ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  Colors.white,
                                ),
                              ),
                            )
                            : Text(
                              widget.editTransaction != null ? 'تحديث' : 'حفظ',
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                  ),
                ),

                const SizedBox(height: 20),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime(2030),
      locale: const Locale('ar', 'IQ'),
    );
    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  Future<void> _selectTime() async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: _selectedTime,
    );
    if (picked != null && picked != _selectedTime) {
      setState(() {
        _selectedTime = picked;
      });
    }
  }

  void _onSavePressed() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final amount = double.parse(_amountController.text);
      final timeString =
          '${_selectedTime.hour.toString().padLeft(2, '0')}:${_selectedTime.minute.toString().padLeft(2, '0')}';

      final transaction = DebtTransaction(
        id: widget.editTransaction?.id,
        customerId: widget.customer.id!,
        amount: amount,
        type: _selectedType,
        date: _selectedDate,
        time: timeString,
        notes:
            _notesController.text.trim().isEmpty
                ? null
                : _notesController.text.trim(),
      );

      final transactionProvider = Provider.of<TransactionProvider>(
        context,
        listen: false,
      );
      final customerProvider = Provider.of<CustomerProvider>(
        context,
        listen: false,
      );

      if (widget.editTransaction != null) {
        // Update existing transaction
        await transactionProvider.updateTransaction(transaction);
      } else {
        // Add new transaction
        await transactionProvider.addTransaction(transaction);
      }

      // Refresh customer data
      await customerProvider.refreshCustomer(widget.customer.id!);

      if (mounted) {
        // Show success notification
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              widget.editTransaction != null
                  ? 'تم تحديث المعاملة بنجاح'
                  : 'تم حفظ البيانات بنجاح',
            ),
            backgroundColor: AppTheme.successColor,
            behavior: SnackBarBehavior.floating,
            margin: const EdgeInsets.only(top: 50, left: 16, right: 16),
          ),
        );

        // Navigate back
        if (widget.isNewCustomer) {
          // Go back to home screen for new customers
          Navigator.popUntil(context, (route) => route.isFirst);
        } else {
          // Go back to previous screen
          Navigator.pop(context);
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ: ${e.toString()}'),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
