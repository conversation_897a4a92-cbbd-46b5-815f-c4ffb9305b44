import 'package:flutter/foundation.dart';
import '../database/database_helper.dart';
import '../models/customer.dart';
import '../services/backup_service.dart';

class CustomerProvider with ChangeNotifier {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  List<Customer> _customers = [];
  List<Customer> _filteredCustomers = [];
  Customer? _selectedCustomer;
  bool _isLoading = false;
  String _searchQuery = '';
  bool _hideAmounts = false;

  List<Customer> get customers => _filteredCustomers;
  Customer? get selectedCustomer => _selectedCustomer;
  bool get isLoading => _isLoading;
  String get searchQuery => _searchQuery;
  bool get hideAmounts => _hideAmounts;

  Future<void> loadCustomers() async {
    _isLoading = true;
    notifyListeners();

    try {
      _customers = await _databaseHelper.getAllCustomers();
      _applyFilter();
    } catch (e) {
      debugPrint('Error loading customers: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> addCustomer(Customer customer) async {
    try {
      final id = await _databaseHelper.insertCustomer(customer);
      final newCustomer = customer.copyWith(id: id);
      _customers.insert(0, newCustomer);
      _applyFilter();

      // تحديث فوري للواجهة
      notifyListeners();

      // إنشاء نسخة احتياطية تلقائية
      BackupService.createAutoBackup();
    } catch (e) {
      debugPrint('Error adding customer: $e');
      rethrow;
    }
  }

  Future<void> updateCustomer(Customer customer) async {
    try {
      await _databaseHelper.updateCustomer(customer);
      final index = _customers.indexWhere((c) => c.id == customer.id);
      if (index != -1) {
        _customers[index] = customer;
        _applyFilter();

        // تحديث فوري للواجهة
        notifyListeners();

        // إنشاء نسخة احتياطية تلقائية
        BackupService.createAutoBackup();
      }
    } catch (e) {
      debugPrint('Error updating customer: $e');
      rethrow;
    }
  }

  Future<void> deleteCustomer(int customerId) async {
    try {
      await _databaseHelper.deleteCustomer(customerId);
      _customers.removeWhere((c) => c.id == customerId);
      _applyFilter();

      // تحديث فوري للواجهة
      notifyListeners();

      // إنشاء نسخة احتياطية تلقائية
      BackupService.createAutoBackup();
    } catch (e) {
      debugPrint('Error deleting customer: $e');
      rethrow;
    }
  }

  void selectCustomer(Customer customer) {
    _selectedCustomer = customer;
    notifyListeners();
  }

  void clearSelectedCustomer() {
    _selectedCustomer = null;
    notifyListeners();
  }

  void searchCustomers(String query) {
    _searchQuery = query;
    _applyFilter();
    notifyListeners();
  }

  void _applyFilter() {
    if (_searchQuery.isEmpty) {
      _filteredCustomers = List.from(_customers);
    } else {
      _filteredCustomers =
          _customers.where((customer) {
            return customer.name.toLowerCase().contains(
                  _searchQuery.toLowerCase(),
                ) ||
                (customer.phone?.toLowerCase().contains(
                      _searchQuery.toLowerCase(),
                    ) ??
                    false) ||
                (customer.address?.toLowerCase().contains(
                      _searchQuery.toLowerCase(),
                    ) ??
                    false);
          }).toList();
    }
  }

  void toggleHideAmounts() {
    _hideAmounts = !_hideAmounts;
    notifyListeners();
  }

  void sortCustomers(String sortBy) {
    switch (sortBy) {
      case 'name':
        _filteredCustomers.sort((a, b) => a.name.compareTo(b.name));
        break;
      case 'amount':
        _filteredCustomers.sort(
          (a, b) => b.remainingAmount.compareTo(a.remainingAmount),
        );
        break;
      case 'date':
        _filteredCustomers.sort((a, b) => b.updatedAt.compareTo(a.updatedAt));
        break;
    }
    notifyListeners();
  }

  double get totalRemainingDebt {
    return _customers.fold(
      0.0,
      (sum, customer) => sum + customer.remainingAmount,
    );
  }

  int get totalCustomers => _customers.length;

  Future<void> refreshCustomer(int customerId) async {
    try {
      final updatedCustomer = await _databaseHelper.getCustomer(customerId);
      if (updatedCustomer != null) {
        final index = _customers.indexWhere((c) => c.id == customerId);
        if (index != -1) {
          _customers[index] = updatedCustomer;
          _applyFilter();
          if (_selectedCustomer?.id == customerId) {
            _selectedCustomer = updatedCustomer;
          }
          notifyListeners();
        }
      }
    } catch (e) {
      debugPrint('Error refreshing customer: $e');
    }
  }
}
