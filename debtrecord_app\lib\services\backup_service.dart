import 'dart:io';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
// import 'package:file_picker/file_picker.dart';
import 'package:share_plus/share_plus.dart';
// import 'package:permission_handler/permission_handler.dart';
import '../database/database_helper.dart';
import '../models/customer.dart';
import '../models/transaction.dart';

class BackupService {
  static const String backupFileName = 'debt_record_backup.json';

  /// إنشاء نسخة احتياطية من البيانات
  static Future<String?> createBackup() async {
    try {
      // طلب الصلاحيات للأندرويد
      if (Platform.isAndroid) {
        var status = await Permission.storage.request();
        if (!status.isGranted) {
          debugPrint(
            'تحذير: لم يتم منح صلاحية التخزين، سيتم الحفظ في المجلد الداخلي',
          );
        }
      }

      final dbHelper = DatabaseHelper();

      // جلب جميع البيانات
      final customers = await dbHelper.getAllCustomers();
      final transactionMaps = await dbHelper.getAllTransactions();

      // التحقق من وجود بيانات
      if (customers.isEmpty && transactionMaps.isEmpty) {
        throw Exception('لا توجد بيانات للنسخ الاحتياطي');
      }

      // تحويل البيانات إلى JSON
      final backupData = {
        'version': '1.0',
        'created_at': DateTime.now().toIso8601String(),
        'app_name': 'سجل الديون',
        'customers': customers.map((c) => c.toMap()).toList(),
        'transactions': transactionMaps,
      };

      final jsonString = jsonEncode(backupData);

      // إنشاء اسم ملف مع التاريخ والوقت
      final now = DateTime.now();
      final timestamp =
          '${now.year}-${now.month.toString().padLeft(2, '0')}-${now.day.toString().padLeft(2, '0')}_${now.hour.toString().padLeft(2, '0')}-${now.minute.toString().padLeft(2, '0')}';
      final fileName = 'debt_backup_$timestamp.json';

      // حفظ الملف في مجلد التطبيق
      final directory = await getApplicationDocumentsDirectory();
      final file = File('${directory.path}/$fileName');
      await file.writeAsString(jsonString);

      return file.path;
    } catch (e) {
      debugPrint('خطأ في إنشاء النسخة الاحتياطية: $e');
      rethrow;
    }
  }

  /// مشاركة النسخة الاحتياطية
  static Future<void> shareBackup() async {
    try {
      final filePath = await createBackup();
      if (filePath != null) {
        await Share.shareXFiles(
          [XFile(filePath)],
          text: 'نسخة احتياطية من تطبيق إدارة الديون',
          subject: 'نسخة احتياطية - ${DateTime.now().toString().split(' ')[0]}',
        );
      }
    } catch (e) {
      throw Exception('فشل في مشاركة النسخة الاحتياطية: $e');
    }
  }

  /// استعادة البيانات من نسخة احتياطية
  static Future<bool> restoreBackup() async {
    try {
      // TODO: إعادة تفعيل file_picker عند حل مشكلة NDK
      debugPrint('ميزة استعادة النسخة الاحتياطية معطلة مؤقتاً');
      return false;

      // اختيار ملف النسخة الاحتياطية
      // FilePickerResult? result = await FilePicker.platform.pickFiles(
      //   type: FileType.custom,
      //   allowedExtensions: ['json'],
      //   allowMultiple: false,
      // );

      // if (result == null || result.files.isEmpty) {
      //   return false;
      // }

      // final file = File(result.files.first.path!);
      // final jsonString = await file.readAsString();
      // final backupData = jsonDecode(jsonString);

      // // التحقق من صحة البيانات
      // if (!_validateBackupData(backupData)) {
      //   throw Exception('ملف النسخة الاحتياطية غير صالح');
      // }

      // final dbHelper = DatabaseHelper();

      // // مسح البيانات الحالية (اختياري - يمكن إضافة خيار للمستخدم)
      // await _clearAllData(dbHelper);

      // // استعادة العملاء
      // final customersData = backupData['customers'] as List;
      // for (var customerMap in customersData) {
      //   try {
      //     final customer = Customer.fromMap(
      //       customerMap as Map<String, dynamic>,
      //     );
      //     await dbHelper.insertCustomer(customer);
      //   } catch (e) {
      //     debugPrint('خطأ في استعادة عميل: $e');
      //     // تجاهل العميل التالف والمتابعة
      //   }
      // }

      // // استعادة المعاملات
      // final transactionsData = backupData['transactions'] as List;
      // for (var transactionMap in transactionsData) {
      //   try {
      //     final transaction = DebtTransaction.fromMap(
      //       transactionMap as Map<String, dynamic>,
      //     );
      //     await dbHelper.insertTransaction(transaction);
      //   } catch (e) {
      //     debugPrint('خطأ في استعادة معاملة: $e');
      //     // تجاهل المعاملة التالفة والمتابعة
      //   }
      // }

      // return true;
    } catch (e) {
      debugPrint('خطأ في استعادة النسخة الاحتياطية: $e');
      rethrow;
    }
  }

  /// التحقق من صحة بيانات النسخة الاحتياطية
  static bool _validateBackupData(Map<String, dynamic> data) {
    return data.containsKey('version') &&
        data.containsKey('customers') &&
        data.containsKey('transactions') &&
        data['customers'] is List &&
        data['transactions'] is List;
  }

  /// مسح جميع البيانات
  static Future<void> _clearAllData(DatabaseHelper dbHelper) async {
    // مسح المعاملات أولاً (بسبب Foreign Key)
    await dbHelper.deleteAllTransactions();
    // ثم مسح العملاء
    await dbHelper.deleteAllCustomers();
  }

  /// الحصول على معلومات النسخة الاحتياطية
  static Future<Map<String, dynamic>?> getBackupInfo() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final file = File('${directory.path}/$backupFileName');

      if (!await file.exists()) {
        return null;
      }

      final stat = await file.stat();
      final jsonString = await file.readAsString();
      final backupData = jsonDecode(jsonString);

      return {
        'file_size': stat.size,
        'created_at': backupData['created_at'],
        'customers_count': (backupData['customers'] as List).length,
        'transactions_count': (backupData['transactions'] as List).length,
        'file_path': file.path,
      };
    } catch (e) {
      return null;
    }
  }

  /// حذف النسخة الاحتياطية المحلية
  static Future<bool> deleteLocalBackup() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final file = File('${directory.path}/$backupFileName');

      if (await file.exists()) {
        await file.delete();
        return true;
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  /// إنشاء نسخة احتياطية تلقائية
  static Future<void> createAutoBackup() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final autoBackupDir = Directory('${directory.path}/auto_backups');

      if (!await autoBackupDir.exists()) {
        await autoBackupDir.create(recursive: true);
      }

      final dbHelper = DatabaseHelper();
      final customers = await dbHelper.getAllCustomers();
      final transactionMaps = await dbHelper.getAllTransactions();

      final backupData = {
        'version': '1.0',
        'created_at': DateTime.now().toIso8601String(),
        'customers': customers.map((c) => c.toMap()).toList(),
        'transactions': transactionMaps,
      };

      final jsonString = jsonEncode(backupData);
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final file = File('${autoBackupDir.path}/auto_backup_$timestamp.json');

      await file.writeAsString(jsonString);

      // الاحتفاظ بآخر 5 نسخ احتياطية فقط
      await _cleanupOldAutoBackups(autoBackupDir);
    } catch (e) {
      // تجاهل الأخطاء في النسخ التلقائية
      debugPrint('فشل في إنشاء نسخة احتياطية تلقائية: $e');
    }
  }

  /// تنظيف النسخ الاحتياطية القديمة
  static Future<void> _cleanupOldAutoBackups(Directory dir) async {
    try {
      final files =
          await dir
              .list()
              .where(
                (entity) =>
                    entity is File && entity.path.contains('auto_backup_'),
              )
              .cast<File>()
              .toList();

      if (files.length > 5) {
        // ترتيب الملفات حسب تاريخ الإنشاء
        files.sort(
          (a, b) => a.statSync().modified.compareTo(b.statSync().modified),
        );

        // حذف الملفات الأقدم
        for (int i = 0; i < files.length - 5; i++) {
          await files[i].delete();
        }
      }
    } catch (e) {
      debugPrint('فشل في تنظيف النسخ الاحتياطية القديمة: $e');
    }
  }
}
