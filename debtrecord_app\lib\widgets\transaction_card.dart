import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import '../models/transaction.dart';
import '../providers/settings_provider.dart';
import '../utils/app_theme.dart';

class TransactionCard extends StatelessWidget {
  final DebtTransaction transaction;
  final VoidCallback onTap;
  final VoidCallback onDelete;

  const TransactionCard({
    super.key,
    required this.transaction,
    required this.onTap,
    required this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<SettingsProvider>(
      builder: (context, settingsProvider, child) {
        return GestureDetector(
          onTap: onTap,
          onLongPress: () => _showOptionsDialog(context),
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: AppTheme.cardDecoration,
            child: Row(
              children: [
                // Left side - Amount, Date, and Time
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        settingsProvider.formatAmount(transaction.amount),
                        style: AppTheme.headingSmall.copyWith(
                          color:
                              transaction.type == TransactionType.debt
                                  ? AppTheme.errorColor
                                  : AppTheme.successColor,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '${transaction.formattedDate} - ${transaction.time}',
                        style: AppTheme.bodySmall.copyWith(
                          color: AppTheme.textSecondaryColor,
                        ),
                      ),
                      if (transaction.notes != null &&
                          transaction.notes!.isNotEmpty) ...[
                        const SizedBox(height: 4),
                        Text(
                          transaction.notes!,
                          style: AppTheme.bodySmall.copyWith(
                            color: AppTheme.textSecondaryColor,
                            fontStyle: FontStyle.italic,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ],
                  ),
                ),

                const SizedBox(width: 16),

                // Right side - Arrow indicating transaction type
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color:
                        transaction.type == TransactionType.debt
                            ? AppTheme.errorColor.withOpacity(0.1)
                            : AppTheme.successColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    transaction.type == TransactionType.debt
                        ? MdiIcons.arrowUp
                        : MdiIcons.arrowDown,
                    color:
                        transaction.type == TransactionType.debt
                            ? AppTheme.errorColor
                            : AppTheme.successColor,
                    size: 24,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void _showOptionsDialog(BuildContext context) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder:
          (context) => Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: AppTheme.textSecondaryColor,
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                const SizedBox(height: 20),

                ListTile(
                  leading: Icon(MdiIcons.pencil, color: AppTheme.primaryColor),
                  title: const Text('تعديل'),
                  onTap: () {
                    Navigator.pop(context);
                    onTap();
                  },
                ),

                ListTile(
                  leading: Icon(MdiIcons.delete, color: AppTheme.errorColor),
                  title: Text(
                    'حذف',
                    style: TextStyle(color: AppTheme.errorColor),
                  ),
                  onTap: () {
                    Navigator.pop(context);
                    onDelete();
                  },
                ),

                const SizedBox(height: 20),
              ],
            ),
          ),
    );
  }
}
