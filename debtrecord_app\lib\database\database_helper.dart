import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../models/customer.dart';
import '../models/transaction.dart';

class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._internal();
  static Database? _database;

  DatabaseHelper._internal();

  factory DatabaseHelper() {
    return _instance;
  }

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    try {
      String databasesPath = await getDatabasesPath();
      String path = join(databasesPath, 'debt_record.db');

      debugPrint('Database path: $path');

      return await openDatabase(
        path,
        version: 1,
        onCreate: _onCreate,
        onOpen: (db) async {
          debugPrint('Database opened successfully');
          // تفعيل Foreign Keys
          await db.execute('PRAGMA foreign_keys = ON');
        },
      );
    } catch (e) {
      debugPrint('Error initializing database: $e');
      rethrow;
    }
  }

  Future<void> _onCreate(Database db, int version) async {
    try {
      debugPrint('Creating database tables...');

      // Create customers table
      await db.execute('''
        CREATE TABLE customers (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL CHECK(length(trim(name)) > 0),
          address TEXT,
          phone TEXT,
          total_debt REAL DEFAULT 0.0,
          total_paid REAL DEFAULT 0.0,
          remaining_amount REAL DEFAULT 0.0,
          created_at TEXT NOT NULL,
          updated_at TEXT NOT NULL
        )
      ''');

      debugPrint('Customers table created successfully');

      // Create transactions table
      await db.execute('''
        CREATE TABLE transactions (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          customer_id INTEGER NOT NULL,
          amount REAL NOT NULL,
          type TEXT NOT NULL CHECK (type IN ('debt', 'payment')),
          date TEXT NOT NULL,
          time TEXT NOT NULL,
          notes TEXT,
          created_at TEXT NOT NULL,
          FOREIGN KEY (customer_id) REFERENCES customers (id) ON DELETE CASCADE
        )
      ''');

      debugPrint('Transactions table created successfully');

      // Create settings table
      await db.execute('''
        CREATE TABLE settings (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          key TEXT UNIQUE NOT NULL,
          value TEXT NOT NULL
        )
      ''');

      debugPrint('Settings table created successfully');

      // Insert default settings
      await db.insert('settings', {'key': 'currency', 'value': 'IQD'});
      await db.insert('settings', {
        'key': 'company_name',
        'value': 'سجل الديون',
      });
      await db.insert('settings', {'key': 'company_logo', 'value': ''});
      await db.insert('settings', {'key': 'printer_size', 'value': 'A4'});
      await db.insert('settings', {'key': 'hide_amounts', 'value': 'false'});

      debugPrint(
        'Database created successfully with all tables and default settings',
      );
    } catch (e) {
      debugPrint('Error creating database: $e');
      rethrow;
    }
  }

  // Customer operations
  Future<int> insertCustomer(Customer customer) async {
    try {
      final db = await database;

      // التحقق من صحة البيانات
      if (customer.name.trim().isEmpty) {
        throw Exception('اسم العميل مطلوب');
      }

      final customerMap = customer.toMap();
      debugPrint('Inserting customer: $customerMap');

      final result = await db.insert('customers', customerMap);
      debugPrint('Customer inserted with ID: $result');

      return result;
    } catch (e) {
      debugPrint('Error inserting customer: $e');
      throw Exception('فشل في إضافة العميل: $e');
    }
  }

  Future<List<Customer>> getAllCustomers() async {
    try {
      final db = await database;
      final List<Map<String, dynamic>> maps = await db.query(
        'customers',
        orderBy: 'updated_at DESC',
      );
      debugPrint('Retrieved ${maps.length} customers from database');
      return List.generate(maps.length, (i) => Customer.fromMap(maps[i]));
    } catch (e) {
      debugPrint('Error getting all customers: $e');
      throw Exception('فشل في جلب العملاء: $e');
    }
  }

  Future<Customer?> getCustomer(int id) async {
    try {
      final db = await database;
      final List<Map<String, dynamic>> maps = await db.query(
        'customers',
        where: 'id = ?',
        whereArgs: [id],
      );

      if (maps.isNotEmpty) {
        return Customer.fromMap(maps.first);
      }
      return null;
    } catch (e) {
      debugPrint('Error getting customer: $e');
      throw Exception('فشل في جلب العميل: $e');
    }
  }

  Future<int> updateCustomer(Customer customer) async {
    try {
      final db = await database;

      // التحقق من صحة البيانات
      if (customer.id == null) {
        throw Exception('معرف العميل مطلوب للتحديث');
      }

      if (customer.name.trim().isEmpty) {
        throw Exception('اسم العميل مطلوب');
      }

      final customerMap = customer.toMap();
      debugPrint('Updating customer: $customerMap');

      final result = await db.update(
        'customers',
        customerMap,
        where: 'id = ?',
        whereArgs: [customer.id],
      );

      debugPrint('Customer updated, affected rows: $result');
      return result;
    } catch (e) {
      debugPrint('Error updating customer: $e');
      throw Exception('فشل في تحديث العميل: $e');
    }
  }

  Future<int> deleteCustomer(int id) async {
    try {
      final db = await database;
      debugPrint('Deleting customer with ID: $id');

      final result = await db.delete(
        'customers',
        where: 'id = ?',
        whereArgs: [id],
      );
      debugPrint('Customer deleted, affected rows: $result');

      return result;
    } catch (e) {
      debugPrint('Error deleting customer: $e');
      throw Exception('فشل في حذف العميل: $e');
    }
  }

  // Transaction operations
  Future<int> insertTransaction(DebtTransaction transaction) async {
    try {
      final db = await database;

      // التحقق من صحة البيانات
      if (transaction.customerId <= 0) {
        throw Exception('معرف العميل غير صحيح');
      }

      if (transaction.amount <= 0) {
        throw Exception('المبلغ يجب أن يكون أكبر من صفر');
      }

      final transactionMap = transaction.toMap();
      debugPrint('Inserting transaction: $transactionMap');

      final result = await db.insert('transactions', transactionMap);
      debugPrint('Transaction inserted with ID: $result');

      // Update customer totals
      await _updateCustomerTotals(transaction.customerId);

      return result;
    } catch (e) {
      debugPrint('Error inserting transaction: $e');
      throw Exception('فشل في إضافة المعاملة: $e');
    }
  }

  Future<List<DebtTransaction>> getCustomerTransactions(int customerId) async {
    try {
      final db = await database;
      final List<Map<String, dynamic>> maps = await db.query(
        'transactions',
        where: 'customer_id = ?',
        whereArgs: [customerId],
        orderBy: 'created_at DESC',
      );
      debugPrint(
        'Retrieved ${maps.length} transactions for customer $customerId',
      );
      return List.generate(
        maps.length,
        (i) => DebtTransaction.fromMap(maps[i]),
      );
    } catch (e) {
      debugPrint('Error getting customer transactions: $e');
      throw Exception('فشل في جلب المعاملات: $e');
    }
  }

  Future<int> updateTransaction(DebtTransaction transaction) async {
    try {
      final db = await database;
      debugPrint('Updating transaction: ${transaction.toMap()}');

      final result = await db.update(
        'transactions',
        transaction.toMap(),
        where: 'id = ?',
        whereArgs: [transaction.id],
      );

      // Update customer totals
      await _updateCustomerTotals(transaction.customerId);

      debugPrint('Transaction updated, affected rows: $result');
      return result;
    } catch (e) {
      debugPrint('Error updating transaction: $e');
      throw Exception('فشل في تحديث المعاملة: $e');
    }
  }

  Future<int> deleteTransaction(int id, int customerId) async {
    try {
      final db = await database;
      debugPrint('Deleting transaction with ID: $id');

      final result = await db.delete(
        'transactions',
        where: 'id = ?',
        whereArgs: [id],
      );

      // Update customer totals
      await _updateCustomerTotals(customerId);

      debugPrint('Transaction deleted, affected rows: $result');
      return result;
    } catch (e) {
      debugPrint('Error deleting transaction: $e');
      throw Exception('فشل في حذف المعاملة: $e');
    }
  }

  Future<void> _updateCustomerTotals(int customerId) async {
    try {
      final db = await database;

      // Calculate totals
      final debtResult = await db.rawQuery(
        'SELECT SUM(amount) as total FROM transactions WHERE customer_id = ? AND type = "debt"',
        [customerId],
      );

      final paymentResult = await db.rawQuery(
        'SELECT SUM(amount) as total FROM transactions WHERE customer_id = ? AND type = "payment"',
        [customerId],
      );

      final totalDebt = (debtResult.first['total'] as double?) ?? 0.0;
      final totalPaid = (paymentResult.first['total'] as double?) ?? 0.0;
      final remainingAmount = totalDebt - totalPaid;

      // Update customer record
      await db.update(
        'customers',
        {
          'total_debt': totalDebt,
          'total_paid': totalPaid,
          'remaining_amount': remainingAmount,
          'updated_at': DateTime.now().toIso8601String(),
        },
        where: 'id = ?',
        whereArgs: [customerId],
      );

      debugPrint(
        'Updated customer $customerId totals: debt=$totalDebt, paid=$totalPaid, remaining=$remainingAmount',
      );
    } catch (e) {
      debugPrint('Error updating customer totals: $e');
      throw Exception('فشل في تحديث إجماليات العميل: $e');
    }
  }

  // Settings operations
  Future<String?> getSetting(String key) async {
    try {
      final db = await database;
      final List<Map<String, dynamic>> maps = await db.query(
        'settings',
        where: 'key = ?',
        whereArgs: [key],
      );

      if (maps.isNotEmpty) {
        return maps.first['value'] as String?;
      }
      return null;
    } catch (e) {
      debugPrint('Error getting setting: $e');
      throw Exception('فشل في جلب الإعداد: $e');
    }
  }

  Future<int> setSetting(String key, String value) async {
    try {
      final db = await database;
      return await db.insert('settings', {
        'key': key,
        'value': value,
      }, conflictAlgorithm: ConflictAlgorithm.replace);
    } catch (e) {
      debugPrint('Error setting value: $e');
      throw Exception('فشل في حفظ الإعداد: $e');
    }
  }

  // Search operations
  Future<List<Customer>> searchCustomers(String query) async {
    try {
      final db = await database;
      final List<Map<String, dynamic>> maps = await db.query(
        'customers',
        where: 'name LIKE ? OR phone LIKE ? OR address LIKE ?',
        whereArgs: ['%$query%', '%$query%', '%$query%'],
        orderBy: 'updated_at DESC',
      );
      return List.generate(maps.length, (i) => Customer.fromMap(maps[i]));
    } catch (e) {
      debugPrint('Error searching customers: $e');
      throw Exception('فشل في البحث عن العملاء: $e');
    }
  }

  // Statistics
  Future<Map<String, dynamic>> getStatistics() async {
    try {
      final db = await database;

      final customerCount = await db.rawQuery(
        'SELECT COUNT(*) as count FROM customers',
      );

      final totalDebtResult = await db.rawQuery(
        'SELECT SUM(total_debt) as total FROM customers',
      );

      final totalPaidResult = await db.rawQuery(
        'SELECT SUM(total_paid) as total FROM customers',
      );

      return {
        'customerCount': customerCount.first['count'] ?? 0,
        'totalDebt': totalDebtResult.first['total'] ?? 0.0,
        'totalPaid': totalPaidResult.first['total'] ?? 0.0,
      };
    } catch (e) {
      debugPrint('Error getting statistics: $e');
      throw Exception('فشل في جلب الإحصائيات: $e');
    }
  }

  // Backup and restore operations
  Future<String> getDatabasePath() async {
    final db = await database;
    return db.path;
  }

  Future<void> restoreDatabase(String backupPath) async {
    await _database?.close();
    _database = null;

    String databasesPath = await getDatabasesPath();
    String dbPath = join(databasesPath, 'debt_record.db');

    File backupFile = File(backupPath);
    await backupFile.copy(dbPath);

    _database = await _initDatabase();
  }

  // New methods for backup and restore
  Future<List<Map<String, dynamic>>> getAllTransactions() async {
    try {
      final db = await database;
      return await db.query('transactions');
    } catch (e) {
      debugPrint('Error getting all transactions: $e');
      throw Exception('فشل في جلب جميع المعاملات: $e');
    }
  }

  Future<int> deleteAllTransactions() async {
    try {
      final db = await database;
      return await db.delete('transactions');
    } catch (e) {
      debugPrint('Error deleting all transactions: $e');
      throw Exception('فشل في حذف جميع المعاملات: $e');
    }
  }

  Future<int> deleteAllCustomers() async {
    try {
      final db = await database;
      return await db.delete('customers');
    } catch (e) {
      debugPrint('Error deleting all customers: $e');
      throw Exception('فشل في حذف جميع العملاء: $e');
    }
  }

  // Test database connection
  Future<bool> testConnection() async {
    try {
      final db = await database;
      await db.rawQuery('SELECT 1');
      debugPrint('Database connection test successful');
      return true;
    } catch (e) {
      debugPrint('Database connection test failed: $e');
      return false;
    }
  }

  // Initialize database for first time use
  Future<void> initializeDatabase() async {
    try {
      debugPrint('Initializing database...');
      final db = await database;

      // Test if tables exist
      final tables = await db.rawQuery(
        "SELECT name FROM sqlite_master WHERE type='table' AND name IN ('customers', 'transactions', 'settings')",
      );

      debugPrint('Found ${tables.length} tables in database');

      if (tables.length < 3) {
        debugPrint('Some tables are missing, recreating database...');
        await _onCreate(db, 1);
      }

      debugPrint('Database initialization completed successfully');
    } catch (e) {
      debugPrint('Database initialization failed: $e');
      rethrow;
    }
  }
}
