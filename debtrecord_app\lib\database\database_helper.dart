import 'dart:io';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../models/customer.dart';
import '../models/transaction.dart';

class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._internal();
  static Database? _database;

  DatabaseHelper._internal();

  factory DatabaseHelper() => _instance;

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    String databasesPath = await getDatabasesPath();
    String path = join(databasesPath, 'debt_record.db');

    return await openDatabase(path, version: 1, onCreate: _onCreate);
  }

  Future<void> _onCreate(Database db, int version) async {
    // Create customers table
    await db.execute('''
      CREATE TABLE customers (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        address TEXT,
        phone TEXT,
        total_debt REAL DEFAULT 0.0,
        total_paid REAL DEFAULT 0.0,
        remaining_amount REAL DEFAULT 0.0,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    ''');

    // Create transactions table
    await db.execute('''
      CREATE TABLE transactions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        customer_id INTEGER NOT NULL,
        amount REAL NOT NULL,
        type TEXT NOT NULL CHECK (type IN ('debt', 'payment')),
        date TEXT NOT NULL,
        time TEXT NOT NULL,
        notes TEXT,
        created_at TEXT NOT NULL,
        FOREIGN KEY (customer_id) REFERENCES customers (id) ON DELETE CASCADE
      )
    ''');

    // Create settings table
    await db.execute('''
      CREATE TABLE settings (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        key TEXT UNIQUE NOT NULL,
        value TEXT NOT NULL
      )
    ''');

    // Insert default settings
    await db.insert('settings', {'key': 'currency', 'value': 'IQD'});
    await db.insert('settings', {'key': 'company_name', 'value': 'سجل الديون'});
    await db.insert('settings', {'key': 'company_logo', 'value': ''});
    await db.insert('settings', {'key': 'printer_size', 'value': 'A4'});
    await db.insert('settings', {'key': 'hide_amounts', 'value': 'false'});
  }

  // Customer operations
  Future<int> insertCustomer(Customer customer) async {
    try {
      final db = await database;

      // التحقق من صحة البيانات
      if (customer.name.trim().isEmpty) {
        throw Exception('اسم العميل مطلوب');
      }

      final customerMap = customer.toMap();
      return await db.insert('customers', customerMap);
    } catch (e) {
      throw Exception('فشل في إضافة العميل: $e');
    }
  }

  Future<List<Customer>> getAllCustomers() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'customers',
      orderBy: 'updated_at DESC',
    );
    return List.generate(maps.length, (i) => Customer.fromMap(maps[i]));
  }

  Future<Customer?> getCustomer(int id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'customers',
      where: 'id = ?',
      whereArgs: [id],
    );
    if (maps.isNotEmpty) {
      return Customer.fromMap(maps.first);
    }
    return null;
  }

  Future<int> updateCustomer(Customer customer) async {
    try {
      final db = await database;

      // التحقق من صحة البيانات
      if (customer.id == null) {
        throw Exception('معرف العميل مطلوب للتحديث');
      }

      if (customer.name.trim().isEmpty) {
        throw Exception('اسم العميل مطلوب');
      }

      final customerMap = customer.toMap();
      return await db.update(
        'customers',
        customerMap,
        where: 'id = ?',
        whereArgs: [customer.id],
      );
    } catch (e) {
      throw Exception('فشل في تحديث العميل: $e');
    }
  }

  Future<int> deleteCustomer(int id) async {
    final db = await database;
    return await db.delete('customers', where: 'id = ?', whereArgs: [id]);
  }

  // Transaction operations
  Future<int> insertTransaction(DebtTransaction transaction) async {
    try {
      final db = await database;

      // التحقق من صحة البيانات
      if (transaction.customerId <= 0) {
        throw Exception('معرف العميل غير صحيح');
      }

      if (transaction.amount <= 0) {
        throw Exception('المبلغ يجب أن يكون أكبر من صفر');
      }

      final transactionMap = transaction.toMap();
      final result = await db.insert('transactions', transactionMap);

      // Update customer totals
      await _updateCustomerTotals(transaction.customerId);

      return result;
    } catch (e) {
      throw Exception('فشل في إضافة المعاملة: $e');
    }
  }

  Future<List<DebtTransaction>> getCustomerTransactions(int customerId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'transactions',
      where: 'customer_id = ?',
      whereArgs: [customerId],
      orderBy: 'date DESC, time DESC',
    );
    return List.generate(maps.length, (i) => DebtTransaction.fromMap(maps[i]));
  }

  Future<int> updateTransaction(DebtTransaction transaction) async {
    final db = await database;
    final result = await db.update(
      'transactions',
      transaction.toMap(),
      where: 'id = ?',
      whereArgs: [transaction.id],
    );

    // Update customer totals
    await _updateCustomerTotals(transaction.customerId);

    return result;
  }

  Future<int> deleteTransaction(int id, int customerId) async {
    final db = await database;
    final result = await db.delete(
      'transactions',
      where: 'id = ?',
      whereArgs: [id],
    );

    // Update customer totals
    await _updateCustomerTotals(customerId);

    return result;
  }

  Future<void> _updateCustomerTotals(int customerId) async {
    final db = await database;

    // Calculate totals
    final debtResult = await db.rawQuery(
      'SELECT SUM(amount) as total FROM transactions WHERE customer_id = ? AND type = "debt"',
      [customerId],
    );

    final paymentResult = await db.rawQuery(
      'SELECT SUM(amount) as total FROM transactions WHERE customer_id = ? AND type = "payment"',
      [customerId],
    );

    double totalDebt = (debtResult.first['total'] as double?) ?? 0.0;
    double totalPaid = (paymentResult.first['total'] as double?) ?? 0.0;
    double remainingAmount = totalDebt - totalPaid;

    // Update customer record
    await db.update(
      'customers',
      {
        'total_debt': totalDebt,
        'total_paid': totalPaid,
        'remaining_amount': remainingAmount,
        'updated_at': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [customerId],
    );
  }

  // Settings operations
  Future<String?> getSetting(String key) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'settings',
      where: 'key = ?',
      whereArgs: [key],
    );
    if (maps.isNotEmpty) {
      return maps.first['value'] as String;
    }
    return null;
  }

  Future<int> setSetting(String key, String value) async {
    final db = await database;
    return await db.insert('settings', {
      'key': key,
      'value': value,
    }, conflictAlgorithm: ConflictAlgorithm.replace);
  }

  // Search operations
  Future<List<Customer>> searchCustomers(String query) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'customers',
      where: 'name LIKE ? OR phone LIKE ? OR address LIKE ?',
      whereArgs: ['%$query%', '%$query%', '%$query%'],
      orderBy: 'updated_at DESC',
    );
    return List.generate(maps.length, (i) => Customer.fromMap(maps[i]));
  }

  // Statistics
  Future<Map<String, dynamic>> getStatistics() async {
    final db = await database;

    final customerCount = await db.rawQuery(
      'SELECT COUNT(*) as count FROM customers',
    );
    final totalDebtResult = await db.rawQuery(
      'SELECT SUM(remaining_amount) as total FROM customers',
    );

    return {
      'total_customers': customerCount.first['count'] as int,
      'total_remaining_debt':
          (totalDebtResult.first['total'] as double?) ?? 0.0,
    };
  }

  // Backup and restore
  Future<String> getDatabasePath() async {
    final db = await database;
    return db.path;
  }

  Future<void> restoreDatabase(String backupPath) async {
    await _database?.close();
    _database = null;

    String databasesPath = await getDatabasesPath();
    String dbPath = join(databasesPath, 'debt_record.db');

    File backupFile = File(backupPath);
    await backupFile.copy(dbPath);

    _database = await _initDatabase();
  }

  // New methods for backup and restore
  Future<List<Map<String, dynamic>>> getAllTransactions() async {
    final db = await database;
    return await db.query('transactions');
  }

  Future<int> deleteAllTransactions() async {
    final db = await database;
    return await db.delete('transactions');
  }

  Future<int> deleteAllCustomers() async {
    final db = await database;
    return await db.delete('customers');
  }
}
