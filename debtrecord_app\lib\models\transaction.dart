enum TransactionType { debt, payment }

class DebtTransaction {
  final int? id;
  final int customerId;
  final double amount;
  final TransactionType type;
  final DateTime date;
  final String time;
  final String? notes;
  final DateTime createdAt;

  DebtTransaction({
    this.id,
    required this.customerId,
    required this.amount,
    required this.type,
    required this.date,
    required this.time,
    this.notes,
    DateTime? createdAt,
  }) : createdAt = createdAt ?? DateTime.now();

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'customer_id': customerId,
      'amount': amount,
      'type': type.name,
      'date': date.toIso8601String().split('T')[0], // YYYY-MM-DD format
      'time': time,
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
    };
  }

  factory DebtTransaction.fromMap(Map<String, dynamic> map) {
    return DebtTransaction(
      id: map['id']?.toInt(),
      customerId: map['customer_id']?.toInt() ?? 0,
      amount: map['amount']?.toDouble() ?? 0.0,
      type: TransactionType.values.firstWhere(
        (e) => e.name == map['type'],
        orElse: () => TransactionType.debt,
      ),
      date: DateTime.parse(map['date']),
      time: map['time'] ?? '',
      notes: map['notes'],
      createdAt: DateTime.parse(map['created_at']),
    );
  }

  DebtTransaction copyWith({
    int? id,
    int? customerId,
    double? amount,
    TransactionType? type,
    DateTime? date,
    String? time,
    String? notes,
    DateTime? createdAt,
  }) {
    return DebtTransaction(
      id: id ?? this.id,
      customerId: customerId ?? this.customerId,
      amount: amount ?? this.amount,
      type: type ?? this.type,
      date: date ?? this.date,
      time: time ?? this.time,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  String get typeDisplayName {
    switch (type) {
      case TransactionType.debt:
        return 'دين';
      case TransactionType.payment:
        return 'تسديد';
    }
  }

  String get formattedDate {
    return '${date.day}/${date.month}/${date.year}';
  }

  @override
  String toString() {
    return 'DebtTransaction{id: $id, customerId: $customerId, amount: $amount, type: $type, date: $date, time: $time, notes: $notes}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is DebtTransaction && other.id == id;
  }

  @override
  int get hashCode {
    return id.hashCode;
  }
}
