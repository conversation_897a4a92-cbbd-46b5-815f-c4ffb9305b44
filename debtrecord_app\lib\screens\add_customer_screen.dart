import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import '../models/customer.dart';
import '../providers/customer_provider.dart';
import '../utils/app_theme.dart';
import 'add_transaction_screen.dart';

class AddCustomerScreen extends StatefulWidget {
  const AddCustomerScreen({super.key});

  @override
  State<AddCustomerScreen> createState() => _AddCustomerScreenState();
}

class _AddCustomerScreenState extends State<AddCustomerScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _addressController = TextEditingController();
  final _phoneController = TextEditingController();

  bool _isLoading = false;

  @override
  void dispose() {
    _nameController.dispose();
    _addressController.dispose();
    _phoneController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: const Text('إضافة عميل جديد'),
        backgroundColor: AppTheme.surfaceColor,
        elevation: 0,
        leading: IconButton(
          onPressed: () => Navigator.pop(context),
          icon: const Icon(Icons.arrow_back),
        ),
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                const SizedBox(height: 20),

                // Name Field (Required)
                Container(
                  decoration: AppTheme.cardDecoration,
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Row(
                        children: [
                          Icon(MdiIcons.account, color: AppTheme.primaryColor),
                          const SizedBox(width: 8),
                          const Expanded(
                            child: Text(
                              'الاسم *',
                              style: AppTheme.labelLarge,
                              textAlign: TextAlign.right,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      TextFormField(
                        controller: _nameController,
                        textDirection: TextDirection.rtl,
                        textAlign: TextAlign.right,
                        style: AppTheme.bodyMedium,
                        decoration: const InputDecoration(
                          hintText: 'أدخل اسم العميل',
                        ),
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'الاسم مطلوب';
                          }
                          return null;
                        },
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 16),

                // Address Field
                Container(
                  decoration: AppTheme.cardDecoration,
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Row(
                        children: [
                          Icon(
                            MdiIcons.mapMarker,
                            color: AppTheme.primaryColor,
                          ),
                          const SizedBox(width: 8),
                          const Expanded(
                            child: Text(
                              'العنوان',
                              style: AppTheme.labelLarge,
                              textAlign: TextAlign.right,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      TextFormField(
                        controller: _addressController,
                        textDirection: TextDirection.rtl,
                        textAlign: TextAlign.right,
                        style: AppTheme.bodyMedium,
                        decoration: const InputDecoration(
                          hintText: 'أدخل عنوان العميل',
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 16),

                // Phone Field
                Container(
                  decoration: AppTheme.cardDecoration,
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Row(
                        children: [
                          Icon(MdiIcons.phone, color: AppTheme.primaryColor),
                          const SizedBox(width: 8),
                          const Expanded(
                            child: Text(
                              'رقم الهاتف',
                              style: AppTheme.labelLarge,
                              textAlign: TextAlign.right,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      TextFormField(
                        controller: _phoneController,
                        textDirection: TextDirection.ltr,
                        textAlign: TextAlign.left,
                        style: AppTheme.bodyMedium,
                        keyboardType: TextInputType.phone,
                        decoration: const InputDecoration(
                          hintText: '07xxxxxxxxx',
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 32),

                // Next Button
                SizedBox(
                  height: 50,
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _onNextPressed,
                    child:
                        _isLoading
                            ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  Colors.white,
                                ),
                              ),
                            )
                            : const Text(
                              'التالي',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                  ),
                ),

                const SizedBox(height: 20),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _onNextPressed() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // Create customer object
      final customer = Customer(
        name: _nameController.text.trim(),
        address:
            _addressController.text.trim().isEmpty
                ? null
                : _addressController.text.trim(),
        phone:
            _phoneController.text.trim().isEmpty
                ? null
                : _phoneController.text.trim(),
      );

      // Add customer to database
      final customerProvider = Provider.of<CustomerProvider>(
        context,
        listen: false,
      );
      await customerProvider.addCustomer(customer);

      // Get the newly added customer (it will have an ID now)
      final addedCustomer = customerProvider.customers.first;

      // Navigate to add transaction screen
      if (mounted) {
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(
            builder:
                (context) => AddTransactionScreen(
                  customer: addedCustomer,
                  isNewCustomer: true,
                ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        String errorMessage = 'حدث خطأ غير متوقع';

        // تخصيص رسالة الخطأ حسب نوع الخطأ
        if (e.toString().contains('اسم العميل مطلوب')) {
          errorMessage = 'يرجى إدخال اسم العميل';
        } else if (e.toString().contains('فشل في إضافة العميل')) {
          errorMessage = 'فشل في حفظ بيانات العميل، يرجى المحاولة مرة أخرى';
        } else if (e.toString().contains('database')) {
          errorMessage = 'خطأ في قاعدة البيانات، يرجى إعادة تشغيل التطبيق';
        }

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(errorMessage),
            backgroundColor: AppTheme.errorColor,
            duration: const Duration(seconds: 4),
            action: SnackBarAction(
              label: 'موافق',
              textColor: Colors.white,
              onPressed: () {
                ScaffoldMessenger.of(context).hideCurrentSnackBar();
              },
            ),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
