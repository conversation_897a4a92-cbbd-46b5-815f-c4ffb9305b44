import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/customer.dart';
import '../providers/customer_provider.dart';
import 'add_transaction_screen.dart';

class AddCustomerScreen extends StatefulWidget {
  const AddCustomerScreen({super.key});

  @override
  State<AddCustomerScreen> createState() => _AddCustomerScreenState();
}

class _AddCustomerScreenState extends State<AddCustomerScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _addressController = TextEditingController();
  final _phoneController = TextEditingController();

  final _nameFocusNode = FocusNode();
  final _addressFocusNode = FocusNode();
  final _phoneFocusNode = FocusNode();

  bool _isLoading = false;
  bool _nameHasFocus = false;
  bool _addressHasFocus = false;
  bool _phoneHasFocus = false;

  @override
  void initState() {
    super.initState();

    _nameFocusNode.addListener(() {
      setState(() {
        _nameHasFocus = _nameFocusNode.hasFocus;
      });
    });

    _addressFocusNode.addListener(() {
      setState(() {
        _addressHasFocus = _addressFocusNode.hasFocus;
      });
    });

    _phoneFocusNode.addListener(() {
      setState(() {
        _phoneHasFocus = _phoneFocusNode.hasFocus;
      });
    });
  }

  @override
  void dispose() {
    _nameController.dispose();
    _addressController.dispose();
    _phoneController.dispose();
    _nameFocusNode.dispose();
    _addressFocusNode.dispose();
    _phoneFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F6FA),
      body: SafeArea(
        child: Column(
          children: [
            // Header
            _buildHeader(),

            // Content
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(24),
                child: Form(
                  key: _formKey,
                  child: Column(
                    children: [
                      const SizedBox(height: 40),

                      // Name Field
                      _buildAnimatedInputField(
                        controller: _nameController,
                        focusNode: _nameFocusNode,
                        labelText: 'الاسم',
                        icon: Icons.person,
                        isRequired: true,
                        hasFocus: _nameHasFocus,
                      ),

                      const SizedBox(height: 24),

                      // Address Field
                      _buildAnimatedInputField(
                        controller: _addressController,
                        focusNode: _addressFocusNode,
                        labelText: 'العنوان',
                        icon: Icons.location_on,
                        hasFocus: _addressHasFocus,
                      ),

                      const SizedBox(height: 24),

                      // Phone Field
                      _buildAnimatedInputField(
                        controller: _phoneController,
                        focusNode: _phoneFocusNode,
                        labelText: 'رقم الهاتف',
                        icon: Icons.phone,
                        keyboardType: TextInputType.phone,
                        textDirection: TextDirection.ltr,
                        hasFocus: _phoneHasFocus,
                      ),

                      const SizedBox(height: 48),

                      // Save Button
                      _buildSaveButton(),

                      const SizedBox(height: 24),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      child: Row(
        children: [
          GestureDetector(
            onTap: () => Navigator.pop(context),
            child: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: const Icon(
                Icons.arrow_back,
                color: Color(0xFF2F3B80),
                size: 24,
              ),
            ),
          ),
          const Spacer(),
          const Text(
            'إضافة شخص جديد',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Color(0xFF2F3B80),
            ),
          ),
          const Spacer(),
          const SizedBox(width: 40), // للتوازن
        ],
      ),
    );
  }

  Widget _buildAnimatedInputField({
    required TextEditingController controller,
    required FocusNode focusNode,
    required String labelText,
    required IconData icon,
    required bool hasFocus,
    bool isRequired = false,
    TextInputType? keyboardType,
    TextDirection? textDirection,
  }) {
    bool hasText = controller.text.isNotEmpty;
    bool shouldFloat = hasFocus || hasText;

    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: hasFocus ? const Color(0xFF2F3B80) : const Color(0xFFE5E7EB),
          width: hasFocus ? 2.5 : 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color:
                hasFocus
                    ? const Color(0xFF2F3B80).withValues(alpha: 0.15)
                    : Colors.black.withValues(alpha: 0.05),
            blurRadius: hasFocus ? 12 : 6,
            offset: const Offset(0, 3),
            spreadRadius: hasFocus ? 1 : 0,
          ),
        ],
      ),
      child: Stack(
        children: [
          // Label
          AnimatedPositioned(
            duration: const Duration(milliseconds: 200),
            top: shouldFloat ? 8 : 20,
            right: shouldFloat ? 20 : 60,
            child: AnimatedDefaultTextStyle(
              duration: const Duration(milliseconds: 200),
              style: TextStyle(
                fontSize: shouldFloat ? 12 : 16,
                color:
                    shouldFloat
                        ? (hasFocus
                            ? const Color(0xFF2F3B80)
                            : const Color(0xFF6B7280))
                        : const Color(0xFF9CA3AF),
                fontWeight: shouldFloat ? FontWeight.w600 : FontWeight.normal,
              ),
              child: Text(labelText),
            ),
          ),

          // Text Field
          Padding(
            padding: EdgeInsets.only(top: shouldFloat ? 28 : 0, bottom: 4),
            child: TextFormField(
              controller: controller,
              focusNode: focusNode,
              textDirection: textDirection ?? TextDirection.rtl,
              textAlign:
                  textDirection == TextDirection.ltr
                      ? TextAlign.left
                      : TextAlign.right,
              keyboardType: keyboardType,
              style: const TextStyle(
                fontSize: 16,
                color: Color(0xFF1F2937),
                fontWeight: FontWeight.w500,
              ),
              decoration: InputDecoration(
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 20,
                  vertical: 16,
                ),
                suffixIcon: AnimatedContainer(
                  duration: const Duration(milliseconds: 200),
                  padding: const EdgeInsets.only(right: 16),
                  child: Icon(
                    icon,
                    color:
                        hasFocus
                            ? const Color(0xFF2F3B80)
                            : const Color(0xFF9CA3AF),
                    size: hasFocus ? 26 : 24,
                  ),
                ),
              ),
              validator:
                  isRequired
                      ? (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'هذا الحقل مطلوب';
                        }
                        return null;
                      }
                      : null,
              onChanged: (value) {
                setState(() {}); // لتحديث حالة النص
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSaveButton() {
    return SizedBox(
      width: double.infinity,
      height: 56,
      child: ElevatedButton(
        onPressed: _isLoading ? null : _onNextPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFF2F3B80),
          foregroundColor: Colors.white,
          elevation: 8,
          shadowColor: const Color(0xFF2F3B80).withValues(alpha: 0.3),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
        ),
        child:
            _isLoading
                ? const SizedBox(
                  width: 24,
                  height: 24,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                )
                : const Text(
                  'التالي',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
      ),
    );
  }

  void _onNextPressed() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // Create customer object
      final customer = Customer(
        name: _nameController.text.trim(),
        address:
            _addressController.text.trim().isEmpty
                ? null
                : _addressController.text.trim(),
        phone:
            _phoneController.text.trim().isEmpty
                ? null
                : _phoneController.text.trim(),
      );

      // Add customer to database
      final customerProvider = Provider.of<CustomerProvider>(
        context,
        listen: false,
      );
      await customerProvider.addCustomer(customer);

      // Get the newly added customer (it will have an ID now)
      final addedCustomer = customerProvider.customers.first;

      // Navigate to add transaction screen
      if (mounted) {
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(
            builder:
                (context) => AddTransactionScreen(
                  customer: addedCustomer,
                  isNewCustomer: true,
                ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        String errorMessage = 'حدث خطأ غير متوقع';

        // تخصيص رسالة الخطأ حسب نوع الخطأ
        if (e.toString().contains('اسم العميل مطلوب')) {
          errorMessage = 'يرجى إدخال اسم العميل';
        } else if (e.toString().contains('فشل في إضافة العميل')) {
          errorMessage = 'فشل في حفظ بيانات العميل، يرجى المحاولة مرة أخرى';
        } else if (e.toString().contains('database')) {
          errorMessage = 'خطأ في قاعدة البيانات، يرجى إعادة تشغيل التطبيق';
        }

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(errorMessage),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 4),
            action: SnackBarAction(
              label: 'موافق',
              textColor: Colors.white,
              onPressed: () {
                ScaffoldMessenger.of(context).hideCurrentSnackBar();
              },
            ),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
