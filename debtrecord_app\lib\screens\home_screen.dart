import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import '../providers/customer_provider.dart';
import '../providers/settings_provider.dart';
import '../utils/app_theme.dart';
import '../widgets/customer_card.dart';
import '../widgets/amount_display_card.dart';
import '../widgets/top_bar.dart';
import 'add_customer_screen.dart';
import 'customer_details_screen.dart';
import 'settings_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final TextEditingController _searchController = TextEditingController();
  String _selectedSort = 'date';

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadData();
    });
  }

  Future<void> _loadData() async {
    final customerProvider = Provider.of<CustomerProvider>(
      context,
      listen: false,
    );
    final settingsProvider = Provider.of<SettingsProvider>(
      context,
      listen: false,
    );

    await Future.wait([
      customerProvider.loadCustomers(),
      settingsProvider.loadSettings(),
    ]);
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: SafeArea(
        child: Column(
          children: [
            // Top Bar
            TopBar(
              searchController: _searchController,
              onSearchChanged: (query) {
                Provider.of<CustomerProvider>(
                  context,
                  listen: false,
                ).searchCustomers(query);
              },
              onSettingsPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const SettingsScreen(),
                  ),
                );
              },
              onBackupPressed: () {
                _showBackupDialog();
              },
              onNotificationsPressed: () {
                _showNotificationsDialog();
              },
            ),

            const SizedBox(height: 16),

            // Amount Display Card
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Consumer2<CustomerProvider, SettingsProvider>(
                builder: (context, customerProvider, settingsProvider, child) {
                  return AmountDisplayCard(
                    totalAmount: customerProvider.totalRemainingDebt,
                    currencySymbol: settingsProvider.currencySymbol,
                    hideAmounts: settingsProvider.hideAmounts,
                    onToggleVisibility: () {
                      settingsProvider.setHideAmounts(
                        !settingsProvider.hideAmounts,
                      );
                    },
                  );
                },
              ),
            ),

            const SizedBox(height: 16),

            // Customers Count and Sort Bar
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
                decoration: AppTheme.cardDecoration,
                child: Row(
                  children: [
                    // Sort Icon (Left)
                    IconButton(
                      onPressed: _showSortDialog,
                      icon: Icon(MdiIcons.sort, color: AppTheme.primaryColor),
                      tooltip: 'ترتيب',
                    ),

                    const Spacer(),

                    // Total Customers (Right)
                    Consumer<CustomerProvider>(
                      builder: (context, customerProvider, child) {
                        return Row(
                          children: [
                            Icon(
                              MdiIcons.accountGroup,
                              color: AppTheme.primaryColor,
                              size: 20,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'مجموع العملاء: ${customerProvider.totalCustomers}',
                              style: AppTheme.bodyMedium.copyWith(
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        );
                      },
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Customers List
            Expanded(
              child: Consumer<CustomerProvider>(
                builder: (context, customerProvider, child) {
                  if (customerProvider.isLoading) {
                    return const Center(child: CircularProgressIndicator());
                  }

                  if (customerProvider.customers.isEmpty) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            MdiIcons.accountOff,
                            size: 64,
                            color: AppTheme.textSecondaryColor,
                          ),
                          const SizedBox(height: 16),
                          Text(
                            customerProvider.searchQuery.isEmpty
                                ? 'لا توجد عملاء مسجلين'
                                : 'لا توجد نتائج للبحث',
                            style: AppTheme.headingSmall.copyWith(
                              color: AppTheme.textSecondaryColor,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            customerProvider.searchQuery.isEmpty
                                ? 'اضغط على زر الإضافة لإضافة عميل جديد'
                                : 'جرب البحث بكلمات مختلفة',
                            style: AppTheme.bodyMedium.copyWith(
                              color: AppTheme.textSecondaryColor,
                            ),
                          ),
                        ],
                      ),
                    );
                  }

                  return RefreshIndicator(
                    onRefresh: () => customerProvider.loadCustomers(),
                    child: ListView.builder(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      itemCount: customerProvider.customers.length,
                      itemBuilder: (context, index) {
                        final customer = customerProvider.customers[index];
                        return Padding(
                          padding: const EdgeInsets.only(bottom: 12),
                          child: CustomerCard(
                            customer: customer,
                            onTap: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder:
                                      (context) => CustomerDetailsScreen(
                                        customer: customer,
                                      ),
                                ),
                              );
                            },
                          ),
                        );
                      },
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),

      // Add Button (Bottom Left)
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => const AddCustomerScreen()),
          );
        },
        child: const Icon(Icons.add),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.startFloat,
    );
  }

  void _showSortDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('ترتيب حسب'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                RadioListTile<String>(
                  title: const Text('التاريخ'),
                  value: 'date',
                  groupValue: _selectedSort,
                  onChanged: (value) {
                    setState(() {
                      _selectedSort = value!;
                    });
                    Provider.of<CustomerProvider>(
                      context,
                      listen: false,
                    ).sortCustomers(_selectedSort);
                    Navigator.pop(context);
                  },
                ),
                RadioListTile<String>(
                  title: const Text('الاسم'),
                  value: 'name',
                  groupValue: _selectedSort,
                  onChanged: (value) {
                    setState(() {
                      _selectedSort = value!;
                    });
                    Provider.of<CustomerProvider>(
                      context,
                      listen: false,
                    ).sortCustomers(_selectedSort);
                    Navigator.pop(context);
                  },
                ),
                RadioListTile<String>(
                  title: const Text('المبلغ'),
                  value: 'amount',
                  groupValue: _selectedSort,
                  onChanged: (value) {
                    setState(() {
                      _selectedSort = value!;
                    });
                    Provider.of<CustomerProvider>(
                      context,
                      listen: false,
                    ).sortCustomers(_selectedSort);
                    Navigator.pop(context);
                  },
                ),
              ],
            ),
          ),
    );
  }

  void _showBackupDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('النسخ الاحتياطي'),
            content: const Text('هذه الميزة ستكون متاحة قريباً'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('موافق'),
              ),
            ],
          ),
    );
  }

  void _showNotificationsDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('الإشعارات'),
            content: const Text('لا توجد إشعارات جديدة'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('موافق'),
              ),
            ],
          ),
    );
  }
}
