import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/customer_provider.dart';
import '../providers/settings_provider.dart';
import 'add_customer_screen.dart';
import 'customer_details_screen.dart';
import 'settings_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final TextEditingController _searchController = TextEditingController();
  String _selectedSort = 'date';

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadData();
    });
  }

  Future<void> _loadData() async {
    final customerProvider = Provider.of<CustomerProvider>(
      context,
      listen: false,
    );
    final settingsProvider = Provider.of<SettingsProvider>(
      context,
      listen: false,
    );

    await Future.wait([
      customerProvider.loadCustomers(),
      settingsProvider.loadSettings(),
    ]);
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FE),
      body: Stack(
        children: [
          // Fixed Top Section
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: Container(
              color: const Color(0xFFF8F9FE),
              child: SafeArea(
                child: Column(
                  children: [
                    // Header with Search and Icons
                    _buildHeader(),

                    // Balance Card
                    _buildBalanceCard(),

                    // Bottom Icons Bar
                    _buildBottomIconsBar(),
                  ],
                ),
              ),
            ),
          ),

          // Content Area
          Positioned(
            top: 200,
            left: 0,
            right: 0,
            bottom: 0,
            child: Container(
              color: const Color(0xFFF8F9FE),
              child: _buildContentArea(),
            ),
          ),

          // Add Button
          Positioned(bottom: 25, left: 25, child: _buildAddButton()),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 25, vertical: 15),
      child: Row(
        children: [
          // Search Box
          Expanded(
            child: SizedBox(
              width: 200,
              child: Stack(
                children: [
                  TextField(
                    controller: _searchController,
                    textAlign: TextAlign.right,
                    style: const TextStyle(fontSize: 13),
                    decoration: InputDecoration(
                      hintText: 'ابحث عن عميل...',
                      hintStyle: TextStyle(
                        color: const Color(0xFF2F3B80).withValues(alpha: 0.6),
                        fontSize: 13,
                      ),
                      filled: true,
                      fillColor: Colors.transparent,
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: const BorderSide(
                          color: Color(0xFF2F3B80),
                          width: 2,
                        ),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: const BorderSide(
                          color: Color(0xFF2F3B80),
                          width: 2,
                        ),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: const BorderSide(
                          color: Color(0xFF2F3B80),
                          width: 2,
                        ),
                      ),
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 35,
                        vertical: 8,
                      ),
                    ),
                    onChanged: (query) {
                      Provider.of<CustomerProvider>(
                        context,
                        listen: false,
                      ).searchCustomers(query);
                    },
                  ),
                  Positioned(
                    right: 12,
                    top: 0,
                    bottom: 0,
                    child: Icon(
                      Icons.search,
                      color: const Color(0xFF2F3B80),
                      size: 14,
                    ),
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(width: 25),

          // Header Icons
          Row(
            children: [
              GestureDetector(
                onTap: _showBackupDialog,
                child: const Icon(
                  Icons.cloud_upload_outlined,
                  color: Color(0xFF2F3B80),
                  size: 20,
                ),
              ),
              const SizedBox(width: 25),
              GestureDetector(
                onTap: _showNotificationsDialog,
                child: const Icon(
                  Icons.notifications_outlined,
                  color: Color(0xFF2F3B80),
                  size: 20,
                ),
              ),
              const SizedBox(width: 25),
              GestureDetector(
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const SettingsScreen(),
                    ),
                  );
                },
                child: const Icon(
                  Icons.settings_outlined,
                  color: Color(0xFF2F3B80),
                  size: 20,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildBalanceCard() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
      child: Consumer2<CustomerProvider, SettingsProvider>(
        builder: (context, customerProvider, settingsProvider, child) {
          return Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: const Color(0xFF2F3B80), width: 1.5),
              color: const Color(0xFF2F3B80).withValues(alpha: 0.05),
            ),
            child: Row(
              children: [
                const Icon(
                  Icons.account_balance_wallet_outlined,
                  color: Color(0xFF2F3B80),
                  size: 24,
                ),
                const Spacer(),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      settingsProvider.hideAmounts
                          ? '***'
                          : '${customerProvider.totalRemainingDebt.toStringAsFixed(0)} ${settingsProvider.currencySymbol}',
                      style: const TextStyle(
                        fontSize: 22,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF2F3B80),
                      ),
                    ),
                    const Text(
                      'مجموع المتبقي',
                      style: TextStyle(
                        color: Color(0xFF2F3B80),
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildBottomIconsBar() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
      child: Row(
        children: [
          Consumer<CustomerProvider>(
            builder: (context, customerProvider, child) {
              return Row(
                children: [
                  const Icon(
                    Icons.people_outline,
                    color: Color(0xFF2F3B80),
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    '${customerProvider.totalCustomers}',
                    style: const TextStyle(
                      color: Color(0xFF2F3B80),
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              );
            },
          ),
          const Spacer(),
          GestureDetector(
            onTap: _showSortDialog,
            child: const Icon(
              Icons.swap_vert,
              color: Color(0xFF2F3B80),
              size: 20,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContentArea() {
    return Consumer<CustomerProvider>(
      builder: (context, customerProvider, child) {
        if (customerProvider.isLoading) {
          return const Center(
            child: CircularProgressIndicator(color: Color(0xFF2F3B80)),
          );
        }

        if (customerProvider.customers.isEmpty) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.description_outlined,
                  size: 48,
                  color: Color(0xFF2F3B80),
                ),
                SizedBox(height: 10),
                Text(
                  'لا توجد معاملات حالياً',
                  style: TextStyle(color: Color(0xFF2F3B80), fontSize: 16),
                ),
              ],
            ),
          );
        }

        return RefreshIndicator(
          onRefresh: () => customerProvider.loadCustomers(),
          child: ListView.builder(
            padding: const EdgeInsets.all(20),
            itemCount: customerProvider.customers.length,
            itemBuilder: (context, index) {
              final customer = customerProvider.customers[index];
              return Container(
                margin: const EdgeInsets.only(bottom: 12),
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: const Color(0xFF2F3B80).withValues(alpha: 0.2),
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0xFF2F3B80).withValues(alpha: 0.1),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: ListTile(
                  contentPadding: EdgeInsets.zero,
                  title: Text(
                    customer.name,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF2F3B80),
                    ),
                    textAlign: TextAlign.right,
                  ),
                  subtitle:
                      customer.address?.isNotEmpty == true
                          ? Text(
                            customer.address!,
                            textAlign: TextAlign.right,
                            style: TextStyle(
                              color: const Color(
                                0xFF2F3B80,
                              ).withValues(alpha: 0.7),
                            ),
                          )
                          : null,
                  trailing: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '${customer.remainingAmount.toStringAsFixed(0)} د.ع',
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF2F3B80),
                        ),
                      ),
                      if (customer.phone?.isNotEmpty == true)
                        Text(
                          customer.phone!,
                          style: TextStyle(
                            fontSize: 12,
                            color: const Color(
                              0xFF2F3B80,
                            ).withValues(alpha: 0.6),
                          ),
                        ),
                    ],
                  ),
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder:
                            (context) =>
                                CustomerDetailsScreen(customer: customer),
                      ),
                    );
                  },
                ),
              );
            },
          ),
        );
      },
    );
  }

  Widget _buildAddButton() {
    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const AddCustomerScreen()),
        );
      },
      child: Container(
        width: 50,
        height: 50,
        decoration: BoxDecoration(
          color: const Color(0xFF2F3B80),
          borderRadius: BorderRadius.circular(25),
          boxShadow: [
            BoxShadow(
              color: const Color(0xFF2F3B80).withValues(alpha: 0.2),
              blurRadius: 15,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: const Icon(Icons.add, color: Colors.white, size: 24),
      ),
    );
  }

  void _showSortDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('ترتيب حسب'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                RadioListTile<String>(
                  title: const Text('التاريخ'),
                  value: 'date',
                  groupValue: _selectedSort,
                  onChanged: (value) {
                    setState(() {
                      _selectedSort = value!;
                    });
                    Provider.of<CustomerProvider>(
                      context,
                      listen: false,
                    ).sortCustomers(_selectedSort);
                    Navigator.pop(context);
                  },
                ),
                RadioListTile<String>(
                  title: const Text('الاسم'),
                  value: 'name',
                  groupValue: _selectedSort,
                  onChanged: (value) {
                    setState(() {
                      _selectedSort = value!;
                    });
                    Provider.of<CustomerProvider>(
                      context,
                      listen: false,
                    ).sortCustomers(_selectedSort);
                    Navigator.pop(context);
                  },
                ),
                RadioListTile<String>(
                  title: const Text('المبلغ'),
                  value: 'amount',
                  groupValue: _selectedSort,
                  onChanged: (value) {
                    setState(() {
                      _selectedSort = value!;
                    });
                    Provider.of<CustomerProvider>(
                      context,
                      listen: false,
                    ).sortCustomers(_selectedSort);
                    Navigator.pop(context);
                  },
                ),
              ],
            ),
          ),
    );
  }

  void _showBackupDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('النسخ الاحتياطي'),
            content: const Text('هذه الميزة ستكون متاحة قريباً'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('موافق'),
              ),
            ],
          ),
    );
  }

  void _showNotificationsDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('الإشعارات'),
            content: const Text('لا توجد إشعارات جديدة'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('موافق'),
              ),
            ],
          ),
    );
  }
}
