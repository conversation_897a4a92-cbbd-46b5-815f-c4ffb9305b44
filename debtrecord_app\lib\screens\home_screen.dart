import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/customer_provider.dart';
import '../providers/settings_provider.dart';
import 'add_customer_screen.dart';
import 'customer_details_screen.dart';
import 'settings_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final TextEditingController _searchController = TextEditingController();
  String _selectedSort = 'date';

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadData();
    });
  }

  Future<void> _loadData() async {
    final customerProvider = Provider.of<CustomerProvider>(
      context,
      listen: false,
    );
    final settingsProvider = Provider.of<SettingsProvider>(
      context,
      listen: false,
    );

    await Future.wait([
      customerProvider.loadCustomers(),
      settingsProvider.loadSettings(),
    ]);
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F6FA),
      body: SafeArea(
        child: Column(
          children: [
            // Header with Search and Icons
            _buildHeader(),

            const SizedBox(height: 16),

            // Balance Card
            _buildBalanceCard(),

            const SizedBox(height: 12),

            // Bottom Icons Bar
            _buildBottomIconsBar(),

            const SizedBox(height: 20),

            // Content Area
            Expanded(child: _buildContentArea()),
          ],
        ),
      ),

      // Add Button
      floatingActionButton: _buildAddButton(),
      floatingActionButtonLocation: FloatingActionButtonLocation.startFloat,
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
      child: Row(
        children: [
          // Header Icons (Left side)
          Row(
            children: [
              GestureDetector(
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const SettingsScreen(),
                    ),
                  );
                },
                child: Container(
                  padding: const EdgeInsets.all(8),
                  child: const Icon(
                    Icons.settings_outlined,
                    color: Color(0xFF2F3B80),
                    size: 24,
                  ),
                ),
              ),
              const SizedBox(width: 12),
              GestureDetector(
                onTap: _showNotificationsDialog,
                child: Container(
                  padding: const EdgeInsets.all(8),
                  child: const Icon(
                    Icons.notifications_outlined,
                    color: Color(0xFF2F3B80),
                    size: 24,
                  ),
                ),
              ),
              const SizedBox(width: 12),
              GestureDetector(
                onTap: _showBackupDialog,
                child: Container(
                  padding: const EdgeInsets.all(8),
                  child: const Icon(
                    Icons.cloud_upload_outlined,
                    color: Color(0xFF2F3B80),
                    size: 24,
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(width: 16),

          // Search Box (Right side)
          Expanded(
            child: Container(
              height: 50,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(25),
                gradient: LinearGradient(
                  colors: [Colors.white, const Color(0xFFF8F9FE)],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                border: Border.all(color: const Color(0xFF2F3B80), width: 2),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0xFF2F3B80).withValues(alpha: 0.1),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                    spreadRadius: 1,
                  ),
                  BoxShadow(
                    color: Colors.white.withValues(alpha: 0.8),
                    blurRadius: 6,
                    offset: const Offset(-2, -2),
                  ),
                ],
              ),
              child: Row(
                children: [
                  Padding(
                    padding: const EdgeInsets.only(left: 16),
                    child: Container(
                      padding: const EdgeInsets.all(6),
                      decoration: BoxDecoration(
                        color: const Color(0xFF2F3B80).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Icon(
                        Icons.search,
                        color: Color(0xFF2F3B80),
                        size: 20,
                      ),
                    ),
                  ),
                  Expanded(
                    child: TextField(
                      controller: _searchController,
                      textAlign: TextAlign.right,
                      style: const TextStyle(
                        fontSize: 15,
                        color: Color(0xFF2F3B80),
                        fontWeight: FontWeight.w500,
                      ),
                      decoration: const InputDecoration(
                        hintText: 'ابحث عن شخص...',
                        hintStyle: TextStyle(
                          color: Color(0xFF9CA3AF),
                          fontSize: 15,
                          fontWeight: FontWeight.normal,
                        ),
                        border: InputBorder.none,
                        contentPadding: EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 14,
                        ),
                      ),
                      onChanged: (query) {
                        Provider.of<CustomerProvider>(
                          context,
                          listen: false,
                        ).searchCustomers(query);
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBalanceCard() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Consumer2<CustomerProvider, SettingsProvider>(
        builder: (context, customerProvider, settingsProvider, child) {
          return Container(
            width: double.infinity,
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              border: Border.all(color: const Color(0xFF2F3B80), width: 2),
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: const Color(0xFF2F3B80).withValues(alpha: 0.08),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: const Color(0xFF2F3B80).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.account_balance_wallet,
                    color: Color(0xFF2F3B80),
                    size: 28,
                  ),
                ),
                const Spacer(),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      settingsProvider.hideAmounts
                          ? '***'
                          : '${customerProvider.totalRemainingDebt.toStringAsFixed(0)} ${settingsProvider.currencySymbol}',
                      style: const TextStyle(
                        fontSize: 28,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF2F3B80),
                      ),
                    ),
                    const SizedBox(height: 4),
                    const Text(
                      'مجموع المتبقي',
                      style: TextStyle(
                        color: Color(0xFF6B7280),
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildBottomIconsBar() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Row(
        children: [
          Consumer<CustomerProvider>(
            builder: (context, customerProvider, child) {
              return Row(
                children: [
                  const Icon(
                    Icons.people_outline,
                    color: Color(0xFF2F3B80),
                    size: 22,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    '${customerProvider.totalCustomers}',
                    style: const TextStyle(
                      color: Color(0xFF2F3B80),
                      fontWeight: FontWeight.w600,
                      fontSize: 16,
                    ),
                  ),
                ],
              );
            },
          ),
          const Spacer(),
          GestureDetector(
            onTap: _showSortDialog,
            child: Row(
              children: [
                const Text(
                  'الترتيب',
                  style: TextStyle(
                    color: Color(0xFF2F3B80),
                    fontWeight: FontWeight.w500,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(width: 4),
                const Icon(Icons.sort, color: Color(0xFF2F3B80), size: 20),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContentArea() {
    return Consumer<CustomerProvider>(
      builder: (context, customerProvider, child) {
        if (customerProvider.isLoading) {
          return const Center(
            child: CircularProgressIndicator(color: Color(0xFF2F3B80)),
          );
        }

        if (customerProvider.customers.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  padding: const EdgeInsets.all(24),
                  decoration: BoxDecoration(
                    color: const Color(0xFF2F3B80).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: const Icon(
                    Icons.description_outlined,
                    size: 64,
                    color: Color(0xFF2F3B80),
                  ),
                ),
                const SizedBox(height: 24),
                const Text(
                  'لا توجد معاملات حالياً',
                  style: TextStyle(
                    color: Color(0xFF6B7280),
                    fontSize: 18,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 8),
                const Text(
                  'اضغط على زر الإضافة لبدء إضافة العملاء',
                  style: TextStyle(color: Color(0xFF9CA3AF), fontSize: 14),
                ),
              ],
            ),
          );
        }

        return RefreshIndicator(
          onRefresh: () => customerProvider.loadCustomers(),
          child: ListView.builder(
            padding: const EdgeInsets.all(20),
            itemCount: customerProvider.customers.length,
            itemBuilder: (context, index) {
              final customer = customerProvider.customers[index];
              return Container(
                margin: const EdgeInsets.only(bottom: 12),
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: const Color(0xFF2F3B80).withValues(alpha: 0.2),
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0xFF2F3B80).withValues(alpha: 0.1),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: ListTile(
                  contentPadding: EdgeInsets.zero,
                  title: Text(
                    customer.name,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF2F3B80),
                    ),
                    textAlign: TextAlign.right,
                  ),
                  subtitle:
                      customer.address?.isNotEmpty == true
                          ? Text(
                            customer.address!,
                            textAlign: TextAlign.right,
                            style: TextStyle(
                              color: const Color(
                                0xFF2F3B80,
                              ).withValues(alpha: 0.7),
                            ),
                          )
                          : null,
                  trailing: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '${customer.remainingAmount.toStringAsFixed(0)} د.ع',
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF2F3B80),
                        ),
                      ),
                      if (customer.phone?.isNotEmpty == true)
                        Text(
                          customer.phone!,
                          style: TextStyle(
                            fontSize: 12,
                            color: const Color(
                              0xFF2F3B80,
                            ).withValues(alpha: 0.6),
                          ),
                        ),
                    ],
                  ),
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder:
                            (context) =>
                                CustomerDetailsScreen(customer: customer),
                      ),
                    );
                  },
                ),
              );
            },
          ),
        );
      },
    );
  }

  Widget _buildAddButton() {
    return FloatingActionButton(
      onPressed: () {
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const AddCustomerScreen()),
        );
      },
      backgroundColor: const Color(0xFF2F3B80),
      foregroundColor: Colors.white,
      elevation: 8,
      child: const Icon(Icons.add, size: 28),
    );
  }

  void _showSortDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('ترتيب حسب'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                RadioListTile<String>(
                  title: const Text('التاريخ'),
                  value: 'date',
                  groupValue: _selectedSort,
                  onChanged: (value) {
                    setState(() {
                      _selectedSort = value!;
                    });
                    Provider.of<CustomerProvider>(
                      context,
                      listen: false,
                    ).sortCustomers(_selectedSort);
                    Navigator.pop(context);
                  },
                ),
                RadioListTile<String>(
                  title: const Text('الاسم'),
                  value: 'name',
                  groupValue: _selectedSort,
                  onChanged: (value) {
                    setState(() {
                      _selectedSort = value!;
                    });
                    Provider.of<CustomerProvider>(
                      context,
                      listen: false,
                    ).sortCustomers(_selectedSort);
                    Navigator.pop(context);
                  },
                ),
                RadioListTile<String>(
                  title: const Text('المبلغ'),
                  value: 'amount',
                  groupValue: _selectedSort,
                  onChanged: (value) {
                    setState(() {
                      _selectedSort = value!;
                    });
                    Provider.of<CustomerProvider>(
                      context,
                      listen: false,
                    ).sortCustomers(_selectedSort);
                    Navigator.pop(context);
                  },
                ),
              ],
            ),
          ),
    );
  }

  void _showBackupDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('النسخ الاحتياطي'),
            content: const Text('هذه الميزة ستكون متاحة قريباً'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('موافق'),
              ),
            ],
          ),
    );
  }

  void _showNotificationsDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('الإشعارات'),
            content: const Text('لا توجد إشعارات جديدة'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('موافق'),
              ),
            ],
          ),
    );
  }
}
