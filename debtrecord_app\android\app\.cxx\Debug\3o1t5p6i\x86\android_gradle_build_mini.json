{"buildFiles": ["C:\\src\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\debt_record\\debtrecord_app\\android\\app\\.cxx\\Debug\\3o1t5p6i\\x86", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\debt_record\\debtrecord_app\\android\\app\\.cxx\\Debug\\3o1t5p6i\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}